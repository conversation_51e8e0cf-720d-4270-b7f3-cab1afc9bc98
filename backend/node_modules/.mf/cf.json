{"clientTcpRtt": 2, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 396982, "clientAcceptEncoding": "gzip, deflate, br", "country": "HK", "isEUCountry": false, "verifiedBotCategory": "", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "B8raVSNdDkR8j1rxJizfv91mU3tg9Vn1PATp8+Fq9HM=", "tlsExportedAuthenticator": {"clientFinished": "d3c5e034e9b34511ad10d7664b6029ff037a704d2f190b013359b93bc5b1e0395fc9176c2b94497cb9f32fba8d9df2df", "clientHandshake": "ed6a20a6da6686849a447dd61c5b763d92bb2d0e74b234c3417dff6c00009eb71c926e24860c15f0101006d1ab122cae", "serverHandshake": "eef7da2105584a87b4c43cb0bf467c9b5c90cdd60f446760dedc97ff772c70ecf981e8ec97e9e0fbb4c02ef29dc781d9", "serverFinished": "25b718e6e565a6c63a568549ca4b1c0f0d85259c79c3b61f76fe8a43dfb04b46571a1f0a521591ff259addded6dbd0e8"}, "tlsClientHelloLength": "386", "colo": "HKG", "timezone": "Asia/Hong_Kong", "longitude": "114.17469", "latitude": "22.27832", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "999077", "city": "Hong Kong", "tlsVersion": "TLSv1.3", "asOrganization": "Google LLC", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}