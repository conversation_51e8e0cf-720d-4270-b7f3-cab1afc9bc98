#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/update-browserslist-db/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules:/Users/<USER>/Desktop/Keepsake-dev/node_modules:/Users/<USER>/Desktop/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/update-browserslist-db/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules:/Users/<USER>/Desktop/Keepsake-dev/node_modules:/Users/<USER>/Desktop/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/Desktop/Keepsake-dev/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../update-browserslist-db/cli.js" "$@"
else
  exec node  "$basedir/../update-browserslist-db/cli.js" "$@"
fi
