// @ts-nocheck
/**
 * 故事相关API处理器
 */

import { Hono } from 'hono';
import { GeminiService } from '../services/gemini';
import { StorageService } from '../services/storage';
import { authMiddleware } from '../middleware/auth';
import { validateStoryRequest } from '../utils/validation';
import { ApiResponse, CreateStoryRequest, Story, StoryGenerationStatus, ErrorCodes } from '../types/api';
import type { Env } from '../types/hono';

const app = new Hono<{ Bindings: Env }>();

// 应用认证中间件到所有路由（开发环境使用调试模式）
app.use('*', authMiddleware);

/**
 * 获取故事主题列表
 * GET /api/stories/themes
 */
app.get('/themes', (c) => {
  return c.json({
    success: true,
    data: [
      { id: 'adventure', name: '冒险', description: '充满刺激的冒险故事' },
      { id: 'friendship', name: '友谊', description: '关于友谊的温暖故事' },
      { id: 'family', name: '家庭', description: '温馨的家庭故事' },
      { id: 'learning', name: '学习', description: '寓教于乐的学习故事' },
      { id: 'magic', name: '魔法', description: '神奇的魔法世界' },
      { id: 'animals', name: '动物', description: '可爱的动物朋友们' }
    ]
  });
});

/**
 * 获取故事风格列表
 * GET /api/stories/styles
 */
app.get('/styles', (c) => {
  return c.json({
    success: true,
    data: [
      { id: 'cartoon', name: '卡通', description: '可爱的卡通风格' },
      { id: 'watercolor', name: '水彩', description: '温柔的水彩画风' },
      { id: 'sketch', name: '素描', description: '简洁的素描风格' },
      { id: 'fantasy', name: '奇幻', description: '梦幻的奇幻风格' },
      { id: 'realistic', name: '写实', description: '真实的写实风格' },
      { id: 'anime', name: '动漫', description: '精美的动漫风格' }
    ]
  });
});

/**
 * 获取声音类型列表
 * GET /api/stories/voices
 */
app.get('/voices', (c) => {
  return c.json({
    success: true,
    data: [
      { id: 'gentle_female', name: '温柔女声', description: '温柔亲切的女性声音' },
      { id: 'warm_male', name: '温暖男声', description: '温暖有力的男性声音' },
      { id: 'child_friendly', name: '儿童友好', description: '适合儿童的活泼声音' },
      { id: 'storyteller', name: '故事讲述者', description: '专业的故事讲述声音' }
    ]
  });
});

/**
 * 创建新故事
 * POST /api/stories
 */
app.post('/', async (c) => {
  try {
    // 🔒 CRITICAL: 确保用户已认证，不允许fallback
    const user = c.get('user');
    if (!user) {
      console.error('故事创建失败: 用户未认证');
      return c.json({
        success: false,
        error: '用户未认证，请先登录',
        code: 'UNAUTHORIZED'
      }, 401);
    }

    console.log('认证用户创建故事:', { userId: user.id, email: user.email });

    // 🔍 数据一致性检查
    const { DataConsistencyMonitor } = await import('../utils/dataConsistencyMonitor');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
    const monitor = new DataConsistencyMonitor(storageService);

    const consistencyCheck = await monitor.checkUserConsistency(user.id);
    if (!consistencyCheck.isConsistent) {
      console.error('🚨 用户数据一致性检查失败:', consistencyCheck);
      return c.json({
        success: false,
        error: '用户数据异常，请联系客服',
        code: 'USER_DATA_INCONSISTENT',
        details: consistencyCheck.issues
      }, 400);
    }

    if (consistencyCheck.warnings.length > 0) {
      console.warn('⚠️ 用户数据一致性警告:', consistencyCheck.warnings);
    }

    const request: CreateStoryRequest = await c.req.json();
    console.log('收到故事创建请求:', request);

    // 基本验证 - 统一使用voice字段
    const requiredFields = ['characterName', 'characterAge', 'characterTraits', 'theme', 'setting', 'style', 'voice'];
    for (const field of requiredFields) {
      if (!request[field]) {
        return c.json({
          success: false,
          error: `缺少必需字段: ${field}`,
          code: 'MISSING_FIELD'
        }, 400);
      }
    }

    // 创建故事对象（不包含id，让StorageService生成）
    const storyData = {
      title: `${request.characterName}的${getThemeName(request.theme)}`,
      characterName: request.characterName,
      characterAge: request.characterAge,
      characterTraits: request.characterTraits,
      theme: request.theme,
      setting: request.setting,
      style: request.style,
      voice: request.voice, // 使用voice字段匹配数据库schema
      customPrompt: request.customPrompt || '',
      status: 'preparing' as const,
      pages: [],
      userId: user.id
    };

    console.log('创建的故事数据:', storyData);

    // 调试环境变量
    console.log('环境变量检查:');
    console.log('CACHE:', typeof c.env.CACHE, c.env.CACHE ? '已定义' : '未定义');
    console.log('ASSETS:', typeof c.env.ASSETS, c.env.ASSETS ? '已定义' : '未定义');
    console.log('DB:', typeof c.env.DB, c.env.DB ? '已定义' : '未定义');
    console.log('GEMINI_API_KEY:', typeof c.env.GEMINI_API_KEY, c.env.GEMINI_API_KEY ? '已定义' : '未定义');

    // 初始化服务 (storageService已在上面声明)
    const geminiService = new GeminiService(c.env.GEMINI_API_KEY);
    
    // 获取用户的订阅信息
    const userSubscription = await storageService.getUserSubscription(user.id);
    console.log(`👤 用户 ${user.id} 的订阅计划: ${userSubscription?.plan || 'free'}, 状态: ${userSubscription?.status || 'none'}`);
    
    // 导入订阅服务
    const { SubscriptionService } = await import('../services/subscription');
    
    // 检查用户是否超出了故事创建限制
    const userStories = await storageService.getUserStories(user.id);
    const storiesThisMonth = userStories.stories.filter(story => {
      const storyDate = new Date(story.createdAt);
      const now = new Date();
      return storyDate.getMonth() === now.getMonth() && storyDate.getFullYear() === now.getFullYear();
    }).length;
    
    const hasReachedLimit = !SubscriptionService.checkLimit(
      userSubscription, 
      'maxStoriesPerMonth', 
      storiesThisMonth
    );
    
    if (hasReachedLimit) {
      return c.json({
        success: false,
        error: '您已达到本月故事创建限制',
        code: 'LIMIT_REACHED',
        data: {
          currentCount: storiesThisMonth,
          limit: SubscriptionService.getFeatures(userSubscription).limits.maxStoriesPerMonth,
          subscription: userSubscription?.plan || 'free'
        }
      }, 403);
    }

    // 保存初始故事到数据库
    let story;
    let storyId;
    try {
      console.log('准备保存故事到数据库，数据:', JSON.stringify(storyData, null, 2));
      story = await storageService.createStory(storyData);
      storyId = story.id;
      console.log('故事已保存到数据库，ID:', storyId);
    } catch (dbError) {
      console.error('数据库保存失败:', dbError);
      console.error('故事数据:', JSON.stringify(storyData, null, 2));
      throw new Error(`数据库保存失败: ${dbError.message}`);
    }

    // 缓存生成状态 - 使用新的6阶段状态系统
    const initialStatus = {
      storyId: storyId,
      status: 'preparing',
      progress: { text: false, images: false, audio: false },
      estimatedTimeRemaining: 120
    };
    await storageService.cacheStoryStatus(storyId, initialStatus);

    // 启动异步生成过程
    console.log(`开始生成故事内容: ${storyId}`);

    // 检查是否有Durable Objects支持
    const hasDurableObjects = c.env.AI_TASK_QUEUE && typeof c.env.AI_TASK_QUEUE.idFromName === 'function';

    if (hasDurableObjects) {
      try {
        console.log(`✅ 使用Durable Objects生成故事: ${storyId}`);
        console.log(`📋 Durable Objects环境检查:`);
        console.log(`   - AI_TASK_QUEUE绑定: ${c.env.AI_TASK_QUEUE ? '可用' : '不可用'}`);
        console.log(`   - idFromName方法: ${typeof c.env.AI_TASK_QUEUE?.idFromName}`);

        // 获取AI任务队列Durable Object
        console.log(`🔧 创建Durable Object ID for story: ${storyId}`);
        const id = c.env.AI_TASK_QUEUE.idFromName(storyId);
        console.log(`🔧 获取Durable Object stub...`);
        const stub = c.env.AI_TASK_QUEUE.get(id);
        console.log(`✅ Durable Object stub创建成功`);

        // 启动故事生成任务
        const requestPayload = {
          storyId,
          characterName: request.characterName,
          age: request.characterAge,
          traits: request.characterTraits,
          theme: request.theme,
          setting: request.setting,
          style: request.style,
          voice: request.voice,
          userId: user.id
        };
        console.log(`📤 准备发送生成请求到Durable Object:`, JSON.stringify(requestPayload, null, 2));

        // 直接调用Durable Object stub.fetch()方法，避免内部HTTP调用问题
        console.log(`🚀 直接调用Durable Object stub.fetch()...`);

        // 创建内部请求对象
        const generateRequest = new Request('https://internal/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestPayload)
        });

        // 同步调用，等待响应以确保调用成功
        try {
          const response = await stub.fetch(generateRequest);
          console.log(`✅ Durable Object stub响应状态: ${response.status}`);
          const responseText = await response.text();
          console.log(`📋 Durable Object stub响应内容: ${responseText}`);
        } catch (error) {
          console.error(`❌ 故事 ${storyId} Durable Objects stub调用失败，降级到传统模式:`, error);
          console.error(`❌ 错误详情:`, error.stack);
          // 降级到传统方式
          generateStoryContent(storyId, request, geminiService, storageService, c.env)
            .catch((fallbackError) => {
              console.error(`❌ 故事 ${storyId} 传统模式生成也失败:`, fallbackError);
            });
        }

        console.log(`✅ 故事 ${storyId} 已提交到AI任务队列`);
      } catch (doError) {
        console.error(`❌ Durable Objects调用失败，降级到传统模式:`, doError);
        console.error(`❌ DO错误详情:`, doError.stack);
        // 降级到传统方式
        generateStoryContent(storyId, request, geminiService, storageService, c.env)
          .then(() => {
            console.log(`✅ 故事 ${storyId} 生成完成（降级模式）`);
          })
          .catch((fallbackError) => {
            console.error(`❌ 故事 ${storyId} 生成失败（降级模式）:`, fallbackError);
          });
      }
    } else {
      console.log(`Durable Objects不可用，使用传统模式生成故事: ${storyId}`);
      // 直接使用传统方式
      generateStoryContent(storyId, request, geminiService, storageService, c.env)
        .then(() => {
          console.log(`故事 ${storyId} 生成完成`);
        })
        .catch((error) => {
          console.error(`故事 ${storyId} 生成失败:`, error);
        });
    }

    return c.json({
      success: true,
      data: { storyId: storyId, status: initialStatus },
      message: '故事创建成功，正在生成中...'
    });

  } catch (error) {
    console.error('故事创建失败:', error);
    return c.json({
      success: false,
      error: '故事创建失败',
      code: 'CREATION_FAILED'
    }, 500);
  }
});

/**
 * 获取故事列表
 * GET /api/stories
 */
app.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '12');
    const user = c.get('user') || { id: 'test-user' };

    // 初始化存储服务
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 从数据库获取用户的故事列表
    const result = await storageService.getUserStories(user.id, page, limit);

    // 转换为前端期望的PaginatedResponse格式
    const paginatedResponse = {
      items: result.stories,
      total: result.total,
      page,
      limit,
      hasNext: page * limit < result.total,
      hasPrev: page > 1
    };

    return c.json({
      success: true,
      data: paginatedResponse
    });

  } catch (error) {
    console.error('获取故事列表失败:', error);

    // 如果数据库查询失败，返回空列表
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '12');

    return c.json({
      success: true,
      data: {
        items: [],
        total: 0,
        page,
        limit,
        hasNext: false,
        hasPrev: false
      }
    });
  }
});

/**
 * 获取故事详情
 * GET /api/stories/:id
 */
app.get('/:id', async (c) => {
  try {
    const storyId = c.req.param('id');

    // 初始化存储服务
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 从数据库获取故事
    const story = await storageService.getStoryById(storyId);

    if (!story) {
      return c.json({
        success: false,
        error: '故事不存在',
        code: 'STORY_NOT_FOUND'
      }, 404);
    }

    return c.json({
      success: true,
      data: story
    });

  } catch (error) {
    console.error('获取故事详情失败:', error);

    // 如果数据库查询失败，返回基于ID的模拟数据作为后备
    const storyId = c.req.param('id');
    return c.json({
      success: true,
      data: {
        id: storyId,
        title: '故事生成中...',
        characterName: '未知角色',
        characterAge: 6,
        characterTraits: [],
        theme: 'adventure',
        setting: 'unknown',
        style: 'cartoon',
        voice: 'gentle_female',
        status: 'preparing',
        pages: [],
        audioUrl: null,
        userId: 'test-user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    });
  }
});

/**
 * 获取故事生成状态
 * GET /api/stories/:id/status
 */
app.get('/:id/status', (c) => {
  const storyId = c.req.param('id');

  // 模拟不同的生成状态
  const statuses = ['preparing', 'generating_text', 'generating_images', 'generating_audio', 'composing', 'completed', 'failed'];
  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
  const progress = ['completed', 'failed'].includes(randomStatus) ? 100 : Math.floor(Math.random() * 100);

  return c.json({
    success: true,
    data: {
      storyId,
      stage: randomStatus === 'completed' ? 'completed' : randomStatus,
      progress: progress,
      currentStep: randomStatus === 'completed' ? '生成完成' : '正在生成故事内容...',
      estimatedTimeRemaining: randomStatus === 'completed' ? 0 : 60,
      error: randomStatus === 'failed' ? '生成失败，请重试' : undefined
    }
  });
});

/**
 * 获取故事生成状态
 * GET /api/stories/:id/status
 */
app.get('/:id/status', async (c) => {
  try {
    const storyId = c.req.param('id');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 从缓存获取状态
    const status = await storageService.getStoryStatus(storyId);
    
    if (!status) {
      // 如果缓存中没有，检查数据库中的故事状态
      const story = await storageService.getStoryById(storyId);
      if (!story) {
        return c.json<ApiResponse>({
          success: false,
          error: '故事不存在',
          code: ErrorCodes.STORY_NOT_FOUND
        }, 404);
      }

      // 构造状态响应
      const storyStatus: StoryGenerationStatus = {
        storyId: story.id,
        status: story.status,
        progress: {
          text: story.pages.length > 0,
          images: story.pages.some(p => p.imageUrl),
          audio: !!story.audioUrl
        }
      };

      return c.json<ApiResponse<StoryGenerationStatus>>({
        success: true,
        data: storyStatus
      });
    }

    return c.json<ApiResponse<StoryGenerationStatus>>({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('Get story status failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取状态失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 获取故事详情
 * GET /api/stories/:id
 */
app.get('/:id', async (c) => {
  try {
    const storyId = c.req.param('id');
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    const story = await storageService.getStoryById(storyId);
    
    if (!story) {
      return c.json<ApiResponse>({
        success: false,
        error: '故事不存在',
        code: ErrorCodes.STORY_NOT_FOUND
      }, 404);
    }

    // 检查权限（只能查看自己的故事）
    if (story.userId !== user.id) {
      return c.json<ApiResponse>({
        success: false,
        error: '无权访问此故事',
        code: ErrorCodes.UNAUTHORIZED
      }, 403);
    }

    return c.json<ApiResponse<Story>>({
      success: true,
      data: story
    });

  } catch (error) {
    console.error('Get story failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取故事失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 获取故事内容（用于预览）
 * GET /api/stories/:id/content
 */
app.get('/:id/content', async (c) => {
  try {
    const storyId = c.req.param('id');
    const contentType = c.req.query('type'); // text, image, audio
    const user = c.get('user');

    if (!contentType || !['text', 'image', 'audio'].includes(contentType)) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Invalid content type. Must be one of: text, image, audio',
        code: ErrorCodes.INVALID_REQUEST
      }, 400);
    }

    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
    const story = await storageService.getStoryById(storyId);

    if (!story) {
      return c.json<ApiResponse>({
        success: false,
        error: '故事不存在',
        code: ErrorCodes.STORY_NOT_FOUND
      }, 404);
    }

    // 检查权限
    if (story.userId !== user.id) {
      return c.json<ApiResponse>({
        success: false,
        error: '无权访问此故事',
        code: ErrorCodes.UNAUTHORIZED
      }, 403);
    }

    // 根据内容类型返回相应内容
    let content;
    switch (contentType) {
      case 'text':
        if (!story.pages) {
          return c.json<ApiResponse>({
            success: false,
            error: 'Text content not available',
            code: ErrorCodes.CONTENT_NOT_FOUND
          }, 404);
        }
        content = {
          pages: JSON.parse(story.pages).map((page: any, index: number) => ({
            pageNumber: index + 1,
            content: page.text || page.content || ''
          }))
        };
        break;

      case 'image':
        const pages = story.pages ? JSON.parse(story.pages) : [];
        const images = [];

        // 添加封面图片
        if (story.coverImageUrl) {
          images.push({
            pageNumber: 0,
            url: story.coverImageUrl,
            description: '故事封面'
          });
        }

        // 添加页面图片
        pages.forEach((page: any, index: number) => {
          if (page.imageUrl) {
            images.push({
              pageNumber: index + 1,
              url: page.imageUrl,
              description: page.imagePrompt || `第${index + 1}页插图`
            });
          }
        });

        if (images.length === 0) {
          return c.json<ApiResponse>({
            success: false,
            error: 'Image content not available',
            code: ErrorCodes.CONTENT_NOT_FOUND
          }, 404);
        }

        content = { images };
        break;

      case 'audio':
        if (!story.audioUrl) {
          return c.json<ApiResponse>({
            success: false,
            error: 'Audio content not available',
            code: ErrorCodes.CONTENT_NOT_FOUND
          }, 404);
        }
        content = {
          url: story.audioUrl,
          duration: story.audioDuration || 0
        };
        break;

      default:
        return c.json<ApiResponse>({
          success: false,
          error: 'Invalid content type',
          code: ErrorCodes.INVALID_REQUEST
        }, 400);
    }

    return c.json<ApiResponse<any>>({
      success: true,
      data: content
    });

  } catch (error) {
    console.error('Get story content failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取故事内容失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 获取用户的故事列表
 * GET /api/stories
 */
app.get('/', async (c) => {
  try {
    const user = c.get('user');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
    const result = await storageService.getUserStories(user.id, page, limit);

    return c.json<ApiResponse<Story[]>>({
      success: true,
      data: result.stories,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: Math.ceil(result.total / limit)
      }
    });

  } catch (error) {
    console.error('Get user stories failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取故事列表失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 获取故事生成状态
 * GET /api/stories/:id/status
 */
app.get('/:id/status', async (c) => {
  try {
    const storyId = c.req.param('id');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 首先尝试从Durable Objects获取状态
    try {
      const id = c.env.AI_TASK_QUEUE.idFromName(storyId);
      const stub = c.env.AI_TASK_QUEUE.get(id);

      const statusRequest = new Request(`https://dummy.com/status?storyId=${storyId}`, {
        method: 'GET'
      });

      const response = await stub.fetch(statusRequest);
      if (response.ok) {
        const status = await response.json();
        return c.json<ApiResponse>({
          success: true,
          data: status
        });
      }
    } catch (doError) {
      console.warn('Durable Objects状态查询失败，降级到缓存查询:', doError);
    }

    // 降级到缓存查询
    const status = await storageService.getStoryStatus(storyId);
    if (!status) {
      return c.json<ApiResponse>({
        success: false,
        error: '故事状态不存在',
        code: ErrorCodes.STORY_NOT_FOUND
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('Get story status failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取故事状态失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 删除故事
 * DELETE /api/stories/:id
 */
app.delete('/:id', async (c) => {
  try {
    const storyId = c.req.param('id');
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    const story = await storageService.getStoryById(storyId);
    
    if (!story) {
      return c.json<ApiResponse>({
        success: false,
        error: '故事不存在',
        code: ErrorCodes.STORY_NOT_FOUND
      }, 404);
    }

    // 检查权限
    if (story.userId !== user.id) {
      return c.json<ApiResponse>({
        success: false,
        error: '无权删除此故事',
        code: ErrorCodes.UNAUTHORIZED
      }, 403);
    }

    // 删除相关文件
    if (story.audioUrl) {
      const audioKey = story.audioUrl.split('/').pop();
      if (audioKey) {
        await storageService.deleteFile(`audio/${audioKey}`);
      }
    }

    for (const page of story.pages) {
      if ('imageUrl' in page && page.imageUrl) {
        const imageKey = page.imageUrl.split('/').pop();
        if (imageKey) {
          await storageService.deleteFile(`images/${imageKey}`);
        }
      }
    }

    // 删除数据库记录
    await c.env.DB.prepare('DELETE FROM stories WHERE id = ?').bind(storyId).run();

    return c.json<ApiResponse>({
      success: true,
      message: '故事删除成功'
    });

  } catch (error) {
    console.error('Delete story failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '删除故事失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 广播进度更新到WebSocket客户端
 */
async function broadcastProgress(
  storyId: string,
  type: string,
  progress: number,
  env: any
): Promise<void> {
  try {
    // 尝试获取AI任务队列DO来广播消息
    if (env.AI_TASK_QUEUE && typeof env.AI_TASK_QUEUE.idFromName === 'function') {
      const id = env.AI_TASK_QUEUE.idFromName(storyId);
      const stub = env.AI_TASK_QUEUE.get(id);

      // 发送广播消息到DO
      const broadcastRequest = new Request(`https://dummy.com/broadcast`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'taskProgress',
          taskId: `${storyId}-${type}`,
          storyId,
          progress,
          status: progress === 100 ? 'completed' : 'running',
          timestamp: Date.now()
        })
      });

      await stub.fetch(broadcastRequest).catch(error => {
        console.warn(`Failed to broadcast progress for ${storyId}:`, error);
      });
    }
  } catch (error) {
    console.warn(`Failed to broadcast progress for ${storyId}:`, error);
  }
}

/**
 * 异步生成故事内容
 */
async function generateStoryContent(
  storyId: string,
  request: CreateStoryRequest,
  geminiService: GeminiService,
  storageService: StorageService,
  env?: any
): Promise<void> {
  // 获取用户的订阅信息
  const userSubscription = await storageService.getUserSubscription(request.userId);
  try {
    console.log(`[${storyId}] 开始生成故事内容`);

    // 1. 更新状态：准备生成
    await updateStoryStatus(storyId, {
      status: 'preparing',
      progress: { text: false, images: false, audio: false },
      estimatedTimeRemaining: 120
    }, storageService);
    await storageService.updateStory(storyId, { status: 'preparing' });

    // 广播准备开始消息
    if (env) {
      await broadcastProgress(storyId, 'preparing', 0, env);
    }

    console.log(`[${storyId}] 状态已更新：准备生成`);

    // 2. 更新状态：开始生成文本
    await updateStoryStatus(storyId, {
      status: 'generating_text',
      progress: { text: false, images: false, audio: false },
      estimatedTimeRemaining: 100
    }, storageService);
    await storageService.updateStory(storyId, { status: 'generating_text' });

    // 广播文本生成开始消息
    if (env) {
      await broadcastProgress(storyId, 'text', 0, env);
    }

    console.log(`[${storyId}] 状态已更新：开始生成文本`);

    // 1. 生成故事文本
    console.log(`[${storyId}] 调用Gemini API生成故事文本...`);
    console.log(`[${storyId}] 请求参数:`, JSON.stringify(request, null, 2));

    let storyResponse;
    try {
      // 传递用户订阅信息，以便根据订阅级别使用不同的AI模型
      storyResponse = await geminiService.generateStory(request, userSubscription);
      console.log(`[${storyId}] 故事文本生成完成，页数: ${storyResponse.pages.length}`);
      
      // 根据用户订阅级别限制页数
      const { SubscriptionService } = await import('../services/subscription');
      const maxPages = SubscriptionService.getFeatures(userSubscription).limits.maxPagesPerStory;
      
      if (storyResponse.pages.length > maxPages) {
        console.log(`[${storyId}] 页数超出用户订阅限制，从 ${storyResponse.pages.length} 减少到 ${maxPages}`);
        storyResponse.pages = storyResponse.pages.slice(0, maxPages);
        // 更新fullText以匹配截断后的页面
        storyResponse.fullText = storyResponse.pages.map(page => page.text).join('\n\n');
      }
    } catch (geminiError) {
      console.error(`[${storyId}] Gemini API调用失败:`, geminiError);
      console.error(`[${storyId}] 错误详情:`, geminiError.message);
      throw new Error(`Gemini API调用失败: ${geminiError.message}`);
    }
    
    // 内容安全检查
    const isSafe = await geminiService.checkContentSafety(storyResponse.fullText);
    if (!isSafe) {
      throw new Error('生成的内容不符合安全标准，请重试');
    }

    // 更新故事文本
    await storageService.updateStory(storyId, {
      title: storyResponse.title,
      pages: storyResponse.pages
    });

    // 广播文本生成完成
    if (env) {
      await broadcastProgress(storyId, 'text', 100, env);
    }

    // 3. 更新状态：开始生成图片
    await updateStoryStatus(storyId, {
      status: 'generating_images',
      progress: { text: true, images: false, audio: false },
      estimatedTimeRemaining: 70
    }, storageService);
    await storageService.updateStory(storyId, { status: 'generating_images' });

    console.log(`[${storyId}] 状态已更新：开始生成图片`);

    // 生成图片
    console.log(`[${storyId}] 开始生成图片，共 ${storyResponse.pages.length} 张`);

    // 广播图片生成开始
    if (env) {
      await broadcastProgress(storyId, 'image', 0, env);
    }

    const imagePrompts = storyResponse.pages.map(page => page.imagePrompt);
    console.log(`[${storyId}] 图片提示词准备完成`);

    // 传递用户订阅信息，以便根据订阅级别使用不同的图片生成配置
    const images = await geminiService.generateImages(imagePrompts, request.style, userSubscription);
    console.log(`[${storyId}] 图片生成完成，成功生成 ${images.filter(img => img).length} 张图片`);

    // 上传图片并更新页面
    console.log(`[${storyId}] 开始上传图片到存储服务`);
    const updatedPages = await Promise.all(
      storyResponse.pages.map(async (page, index) => {
        if (images[index]) {
          try {
            const imageKey = `images/${storyId}_page_${page.pageNumber}.jpg`;
            console.log(`[${storyId}] 上传第 ${index + 1} 张图片: ${imageKey}`);
            const imageUrl = await storageService.uploadImage(imageKey, images[index]);
            console.log(`[${storyId}] 第 ${index + 1} 张图片上传成功: ${imageUrl}`);

            // 验证上传的URL是否有效
            if (!imageUrl || !imageUrl.startsWith('https://assets.proxypool.eu.org/')) {
              throw new Error(`Invalid image URL returned: ${imageUrl}`);
            }

            return { ...page, imageUrl };
          } catch (error) {
            console.error(`[${storyId}] 第 ${index + 1} 张图片上传失败:`, error);
            // 上传失败时不设置imageUrl，保持页面原有状态
            return page;
          }
        }
        console.log(`[${storyId}] 第 ${index + 1} 张图片生成失败，跳过`);
        return page;
      })
    );
    console.log(`[${storyId}] 所有图片处理完成，成功上传 ${updatedPages.filter(p => p.imageUrl).length} 张图片`);

    await storageService.updateStory(storyId, { pages: updatedPages });

    // 广播图片生成完成
    if (env) {
      await broadcastProgress(storyId, 'image', 100, env);
    }

    // 4. 更新状态：开始生成语音
    await updateStoryStatus(storyId, {
      status: 'generating_audio',
      progress: { text: true, images: true, audio: false },
      estimatedTimeRemaining: 30
    }, storageService);
    await storageService.updateStory(storyId, { status: 'generating_audio' });

    console.log(`[${storyId}] 状态已更新：开始生成语音`);

    // 生成音频
    console.log(`[${storyId}] 开始生成音频`);

    // 广播音频生成开始
    if (env) {
      await broadcastProgress(storyId, 'audio', 0, env);
    }

    let audioUrl: string | null = null;
    try {
      // 传递用户订阅信息，以便根据订阅级别使用不同的音频生成配置
      const audioData = await geminiService.generateAudio(storyResponse.fullText, request.voice, userSubscription);
      const audioKey = `audio/${storyId}.mp3`;
      console.log(`[${storyId}] 开始上传音频: ${audioKey}`);
      audioUrl = await storageService.uploadAudio(audioKey, audioData);
      console.log(`[${storyId}] 音频上传成功: ${audioUrl}`);

      // 验证上传的URL是否有效
      if (!audioUrl || !audioUrl.startsWith('https://assets.proxypool.eu.org/')) {
        throw new Error(`Invalid audio URL returned: ${audioUrl}`);
      }
    } catch (error) {
      console.error(`[${storyId}] 音频生成或上传失败:`, error);
      // 音频生成失败时设置为null，故事仍然可以完成
      audioUrl = null;
    }

    // 设置封面图片（使用第一页的图片）
    const coverImageUrl = updatedPages[0]?.imageUrl || null;

    // 5. 更新状态：最终合成
    await updateStoryStatus(storyId, {
      status: 'composing',
      progress: { text: true, images: true, audio: true },
      estimatedTimeRemaining: 10
    }, storageService);
    await storageService.updateStory(storyId, { status: 'composing' });

    console.log(`[${storyId}] 状态已更新：最终合成`);

    // 广播合成开始
    if (env) {
      await broadcastProgress(storyId, 'composing', 0, env);
    }

    // 最终更新故事
    console.log(`[${storyId}] 更新故事最终状态`);
    const finalUpdateData: any = {
      status: 'completed'
    };

    // 只有在音频URL有效时才更新
    if (audioUrl) {
      finalUpdateData.audioUrl = audioUrl;
    }

    // 只有在封面图片URL有效时才更新
    if (coverImageUrl) {
      finalUpdateData.coverImageUrl = coverImageUrl;
    }

    await storageService.updateStory(storyId, finalUpdateData);
    console.log(`[${storyId}] 故事生成完成，音频: ${audioUrl ? '成功' : '失败'}, 封面: ${coverImageUrl ? '成功' : '失败'}`);

    // 广播音频生成完成和故事完成
    if (env) {
      await broadcastProgress(storyId, 'audio', 100, env);
      // 广播故事完成消息
      try {
        if (env.AI_TASK_QUEUE && typeof env.AI_TASK_QUEUE.idFromName === 'function') {
          const id = env.AI_TASK_QUEUE.idFromName(storyId);
          const stub = env.AI_TASK_QUEUE.get(id);

          const completeRequest = new Request(`https://dummy.com/broadcast`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              type: 'storyCompleted',
              storyId,
              timestamp: Date.now()
            })
          });

          await stub.fetch(completeRequest).catch(error => {
            console.warn(`Failed to broadcast story completion for ${storyId}:`, error);
          });
        }
      } catch (error) {
        console.warn(`Failed to broadcast story completion for ${storyId}:`, error);
      }
    }

    // 扣除用户积分
    const userInfo = await storageService.getUserById(request.userId);
    if (userInfo && userInfo.credits > 0) {
      await storageService.updateUser(userInfo.id, { credits: userInfo.credits - 1 });
    }

    // 更新状态：完成
    await updateStoryStatus(storyId, {
      status: 'completed',
      progress: { text: true, images: true, audio: true },
      estimatedTimeRemaining: 0
    }, storageService);

  } catch (error) {
    console.error('Story generation failed:', error);
    
    // 更新状态：失败
    await updateStoryStatus(storyId, {
      status: 'failed',
      progress: { text: false, images: false, audio: false },
      error: error.message
    }, storageService);

    // 更新数据库状态
    await storageService.updateStory(storyId, { status: 'failed' });
  }
}

/**
 * 更新故事生成状态
 */
async function updateStoryStatus(
  storyId: string,
  status: Partial<StoryGenerationStatus>,
  storageService: StorageService
): Promise<void> {
  const currentStatus = await storageService.getStoryStatus(storyId) || {
    storyId,
    status: 'preparing',
    progress: { text: false, images: false, audio: false }
  };

  const updatedStatus = { ...currentStatus, ...status };
  await storageService.cacheStoryStatus(storyId, updatedStatus);
}

/**
 * 辅助函数：获取主题名称
 */
function getThemeName(themeId: string): string {
  const themes: Record<string, string> = {
    'adventure': '冒险故事',
    'friendship': '友谊故事',
    'family': '家庭故事',
    'learning': '学习故事',
    'magic': '魔法故事',
    'animals': '动物故事'
  };
  return themes[themeId] || '奇妙故事';
}

/**
 * 跳过生成阶段
 * POST /api/stories/:id/skip-stage
 */
app.post('/:id/skip-stage', async (c) => {
  try {
    const storyId = c.req.param('id');
    const user = c.get('user');
    const { stage, reason } = await c.req.json();

    if (!user) {
      return c.json<ApiResponse>({
        success: false,
        error: '未提供有效的认证令牌',
        code: ErrorCodes.UNAUTHORIZED
      }, 403);
    }

    if (!stage || !['text', 'image', 'audio'].includes(stage)) {
      return c.json<ApiResponse>({
        success: false,
        error: '无效的阶段参数',
        code: ErrorCodes.INVALID_REQUEST
      }, 400);
    }

    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 检查故事是否存在且属于当前用户
    const story = await storageService.getStoryById(storyId);
    if (!story) {
      return c.json<ApiResponse>({
        success: false,
        error: '故事不存在',
        code: ErrorCodes.STORY_NOT_FOUND
      }, 404);
    }

    if (story.userId !== user.id) {
      return c.json<ApiResponse>({
        success: false,
        error: '无权限操作此故事',
        code: ErrorCodes.UNAUTHORIZED
      }, 403);
    }

    // 检查故事是否正在生成中
    if (!['generating_text', 'generating_images', 'generating_audio'].includes(story.status)) {
      return c.json<ApiResponse>({
        success: false,
        error: '故事当前不在生成状态，无法跳过阶段',
        code: ErrorCodes.INVALID_REQUEST
      }, 400);
    }

    // 通过Durable Objects发送跳过请求
    const doId = c.env.AI_TASK_QUEUE.idFromName(storyId);
    const doStub = c.env.AI_TASK_QUEUE.get(doId);

    const skipResponse = await doStub.fetch(new Request('https://do/websocket-message', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        storyId,
        message: {
          type: 'skipStage',
          stage,
          reason: reason || '用户手动跳过'
        }
      })
    }));

    if (!skipResponse.ok) {
      throw new Error('跳过阶段请求失败');
    }

    return c.json<ApiResponse>({
      success: true,
      message: `已请求跳过${stage}阶段`,
      data: {
        storyId,
        stage,
        reason: reason || '用户手动跳过'
      }
    });
  } catch (error) {
    console.error('Skip stage error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '跳过阶段失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

export default app;