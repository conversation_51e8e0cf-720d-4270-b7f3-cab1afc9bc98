// @ts-nocheck
/**
 * API 类型定义
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 分页响应类型
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 用户相关类型
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  googleId: string;
  subscription?: UserSubscription;
  credits: number;
  createdAt: string;
  updatedAt: string;
}

export interface UserSubscription {
  id: string;
  userId: string;
  plan: 'free' | 'credits' | 'unlimited' | 'unlimited_monthly';
  status: 'active' | 'canceled' | 'expired';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  stripeSubscriptionId?: string;
}

// 故事相关类型
export interface Story {
  id: string;
  userId: string;
  title: string;
  characterName: string;
  characterAge: number;
  characterTraits: string[];
  theme: string;
  setting: string;
  style: StoryStyle;
  voice: VoiceType;
  pages: StoryPage[];
  audioUrl?: string;
  coverImageUrl?: string;
  status: 'generating' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
}

export interface StoryPage {
  pageNumber: number;
  text: string;
  imageUrl?: string;
  imagePrompt: string;
}

export type StoryStyle = 'cartoon' | 'watercolor' | 'sketch' | 'fantasy' | 'realistic' | 'anime';
export type VoiceType = 'gentle_female' | 'warm_male' | 'child_friendly' | 'storyteller';

// 故事创建请求
export interface CreateStoryRequest {
  characterName: string;
  characterAge: number;
  characterTraits: string[];
  theme: string;
  setting: string;
  style: StoryStyle;
  voice: VoiceType;
  customPrompt?: string;
  userId?: string; // 添加用户ID字段
  skipAudio?: boolean; // 新增：是否跳过音频生成
}

// 故事生成状态
export interface StoryGenerationStatus {
  storyId: string;
  status: 'generating' | 'completed' | 'failed';
  progress: {
    text: boolean;
    images: boolean;
    audio: boolean;
  };
  estimatedTimeRemaining?: number;
  error?: string;
}

// 实体书相关类型
export interface PhysicalBook {
  id: string;
  storyId: string;
  userId: string;
  customization: BookCustomization;
  shippingInfo: ShippingInfo;
  status: 'pending' | 'printing' | 'shipped' | 'delivered';
  orderNumber: string;
  price: number;
  createdAt: string;
  updatedAt: string;
}

export interface BookCustomization {
  coverTitle?: string;
  coverColor: string;
  coverStyle: 'standard' | 'premium' | 'deluxe';
  dedication?: string;
  giftWrap: boolean;
}

export interface ShippingInfo {
  recipientName: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  phone: string;
  email: string;
}

// 支付相关类型
export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
  clientSecret: string;
}

export interface Payment {
  id: string;
  userId: string;
  type: string;
  amount: number;
  currency: string;
  status: string;
  stripePaymentIntentId?: string;
  stripeSubscriptionId?: string;
  metadata?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  stripePriceId: string;
}

// 认证相关类型
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  expiresAt?: number; // 过期时间戳（毫秒）
}

export interface GoogleUserInfo {
  id: string;
  email: string;
  name: string;
  picture?: string;
  verified_email: boolean;
}

// 错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// 常用错误代码
export enum ErrorCodes {
  // 认证错误
  UNAUTHORIZED = 'UNAUTHORIZED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  
  // 用户错误
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS = 'USER_ALREADY_EXISTS',
  
  // 故事错误
  STORY_NOT_FOUND = 'STORY_NOT_FOUND',
  STORY_GENERATION_FAILED = 'STORY_GENERATION_FAILED',
  INSUFFICIENT_CREDITS = 'INSUFFICIENT_CREDITS',
  
  // 支付错误
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  SUBSCRIPTION_NOT_FOUND = 'SUBSCRIPTION_NOT_FOUND',
  
  // 系统错误
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // AI服务错误
  AI_SERVICE_ERROR = 'AI_SERVICE_ERROR',
  CONTENT_SAFETY_VIOLATION = 'CONTENT_SAFETY_VIOLATION',
  
  // 通用错误
  NOT_FOUND = 'NOT_FOUND',
  FORBIDDEN = 'FORBIDDEN',
  CONFLICT = 'CONFLICT',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR'
}