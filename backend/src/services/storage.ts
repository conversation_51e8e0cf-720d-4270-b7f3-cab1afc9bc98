// @ts-nocheck
/**
 * 存储服务
 * 处理 KV、R2、D1 数据存储
 */

import { User, Story, PhysicalBook, UserSubscription, Payment } from '../types/api';

export class StorageService {
  constructor(
    private kv: KVNamespace,
    private r2: R2Bucket,
    private db: D1Database
  ) {}

  // ==================== KV Store 操作 ====================

  /**
   * 缓存用户会话
   */
  async cacheUserSession(userId: string, sessionData: any, ttl: number = 3600): Promise<void> {
    const key = `session:${userId}`;
    await this.kv.put(key, JSON.stringify(sessionData), { expirationTtl: ttl });
  }

  /**
   * 获取用户会话
   */
  async getUserSession(userId: string): Promise<any | null> {
    const key = `session:${userId}`;
    const data = await this.kv.get(key);
    return data ? JSON.parse(data) : null;
  }

  /**
   * 删除用户会话
   */
  async deleteUserSession(userId: string): Promise<void> {
    const key = `session:${userId}`;
    await this.kv.delete(key);
  }

  /**
   * 缓存故事生成状态
   */
  async cacheStoryStatus(storyId: string, status: any, ttl: number = 1800): Promise<void> {
    const key = `story_status:${storyId}`;
    await this.kv.put(key, JSON.stringify(status), { expirationTtl: ttl });
  }

  /**
   * 获取故事生成状态
   */
  async getStoryStatus(storyId: string): Promise<any | null> {
    const key = `story_status:${storyId}`;
    const data = await this.kv.get(key);
    return data ? JSON.parse(data) : null;
  }

  /**
   * 缓存API调用限制
   */
  async setRateLimit(key: string, count: number, ttl: number): Promise<void> {
    await this.kv.put(`rate_limit:${key}`, count.toString(), { expirationTtl: ttl });
  }

  /**
   * 获取API调用次数
   */
  async getRateLimit(key: string): Promise<number> {
    const data = await this.kv.get(`rate_limit:${key}`);
    return data ? parseInt(data) : 0;
  }

  // ==================== R2 Storage 操作 ====================

  /**
   * 上传图片到R2
   */
  async uploadImage(key: string, imageData: string, contentType: string = 'image/jpeg'): Promise<string> {
    try {
      console.log(`[StorageService] 开始上传图片: ${key}`);

      // 处理不同格式的图片数据
      let base64Data: string;

      if (imageData.startsWith('data:')) {
        // 处理data URL格式: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
        const base64Index = imageData.indexOf('base64,');
        if (base64Index === -1) {
          throw new Error('Invalid data URL format: missing base64 data');
        }
        base64Data = imageData.substring(base64Index + 7);
        console.log(`[StorageService] 从data URL提取base64数据，长度: ${base64Data.length}`);
      } else if (imageData.startsWith('http')) {
        // 如果是外部URL，使用uploadImageFromUrl方法
        console.log(`[StorageService] 检测到外部URL，转用uploadImageFromUrl方法: ${imageData}`);
        return await this.uploadImageFromUrl(imageData, key);
      } else {
        // 假设是纯base64数据
        base64Data = imageData;
        console.log(`[StorageService] 使用纯base64数据，长度: ${base64Data.length}`);
      }

      // 验证base64数据
      if (!base64Data || base64Data.length === 0) {
        throw new Error('Empty base64 data');
      }

      // 将base64转换为ArrayBuffer
      const buffer = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0));
      console.log(`[StorageService] base64转换完成，buffer大小: ${buffer.length} bytes`);

      await this.r2.put(key, buffer, {
        httpMetadata: {
          contentType: contentType,
          cacheControl: 'public, max-age=31536000', // 1年缓存
        },
      });

      const publicUrl = `https://assets.proxypool.eu.org/${key}`;
      console.log(`[StorageService] 图片上传成功: ${publicUrl}`);
      return publicUrl;
    } catch (error) {
      console.error(`[StorageService] 图片上传失败 (${key}):`, error);
      throw new Error(`图片上传失败: ${error.message}`);
    }
  }

  /**
   * 从URL下载图片并上传到R2
   */
  async uploadImageFromUrl(imageUrl: string, key: string): Promise<string> {
    try {
      console.log(`Downloading image from URL: ${imageUrl}`);

      // 下载图片
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
      }

      const imageBuffer = await response.arrayBuffer();
      const contentType = response.headers.get('content-type') || 'image/jpeg';

      console.log(`Uploading image to R2 with key: ${key}, size: ${imageBuffer.byteLength} bytes`);

      // 上传到R2
      await this.r2.put(key, imageBuffer, {
        httpMetadata: {
          contentType,
          cacheControl: 'public, max-age=31536000', // 1年缓存
        },
      });

      const publicUrl = `https://assets.proxypool.eu.org/${key}`;
      console.log(`Image uploaded successfully: ${publicUrl}`);

      return publicUrl;
    } catch (error) {
      console.error('从URL上传图片失败:', error);
      throw new Error(`从URL上传图片失败: ${error.message}`);
    }
  }

  /**
   * 上传音频到R2
   */
  async uploadAudio(key: string, audioData: string, contentType: string = 'audio/mpeg'): Promise<string> {
    try {
      console.log(`[StorageService] 开始上传音频: ${key}`);

      // 处理不同格式的音频数据
      let base64Data: string;

      if (audioData.startsWith('data:')) {
        // 处理data URL格式: data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEA...
        const base64Index = audioData.indexOf('base64,');
        if (base64Index === -1) {
          throw new Error('Invalid data URL format: missing base64 data');
        }
        base64Data = audioData.substring(base64Index + 7);
        console.log(`[StorageService] 从data URL提取base64数据，长度: ${base64Data.length}`);
      } else if (audioData.startsWith('http')) {
        // 如果是外部URL，使用uploadAudioFromUrl方法
        console.log(`[StorageService] 检测到外部URL，转用uploadAudioFromUrl方法: ${audioData}`);
        return await this.uploadAudioFromUrl(audioData, key);
      } else {
        // 假设是纯base64数据
        base64Data = audioData;
        console.log(`[StorageService] 使用纯base64数据，长度: ${base64Data.length}`);
      }

      // 验证base64数据
      if (!base64Data || base64Data.length === 0) {
        throw new Error('Empty base64 data');
      }

      // 将base64转换为ArrayBuffer
      const buffer = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0));
      console.log(`[StorageService] base64转换完成，buffer大小: ${buffer.length} bytes`);

      await this.r2.put(key, buffer, {
        httpMetadata: {
          contentType: contentType,
          cacheControl: 'public, max-age=31536000', // 1年缓存
        },
      });

      const publicUrl = `https://assets.proxypool.eu.org/${key}`;
      console.log(`[StorageService] 音频上传成功: ${publicUrl}`);
      return publicUrl;
    } catch (error) {
      console.error(`[StorageService] 音频上传失败 (${key}):`, error);
      throw new Error(`音频上传失败: ${error.message}`);
    }
  }

  /**
   * 从URL下载音频并上传到R2
   */
  async uploadAudioFromUrl(audioUrl: string, key: string): Promise<string> {
    try {
      console.log(`Downloading audio from URL: ${audioUrl}`);

      // 下载音频
      const response = await fetch(audioUrl);
      if (!response.ok) {
        throw new Error(`Failed to download audio: ${response.status} ${response.statusText}`);
      }

      const audioBuffer = await response.arrayBuffer();
      const contentType = response.headers.get('content-type') || 'audio/mpeg';

      console.log(`Uploading audio to R2 with key: ${key}, size: ${audioBuffer.byteLength} bytes`);

      // 上传到R2
      await this.r2.put(key, audioBuffer, {
        httpMetadata: {
          contentType,
          cacheControl: 'public, max-age=31536000', // 1年缓存
        },
      });

      const publicUrl = `https://assets.proxypool.eu.org/${key}`;
      console.log(`Audio uploaded successfully: ${publicUrl}`);

      return publicUrl;
    } catch (error) {
      console.error('从URL上传音频失败:', error);
      throw new Error(`从URL上传音频失败: ${error.message}`);
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(key: string): Promise<void> {
    await this.r2.delete(key);
  }

  /**
   * 获取文件URL
   */
  getFileUrl(key: string): string {
    return `https://assets.proxypool.eu.org/${key}`;
  }

  // ==================== D1 Database 操作 ====================

  /**
   * 创建用户
   */
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const result = await this.db.prepare(`
      INSERT INTO users (id, email, name, avatar, google_id, credits, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id,
      userData.email,
      userData.name,
      userData.avatar || null,
      userData.googleId,
      userData.credits || 1, // 新用户赠送1个免费故事
      now,
      now
    ).run();

    if (!result.success) {
      throw new Error('用户创建失败');
    }

    return {
      id,
      ...userData,
      credits: userData.credits || 1,
      createdAt: now,
      updatedAt: now
    };
  }

  /**
   * 根据ID获取用户
   */
  async getUserById(userId: string): Promise<User | null> {
    const result = await this.db.prepare(`
      SELECT * FROM users WHERE id = ?
    `).bind(userId).first();

    if (!result) return null;

    return this.mapDbUserToUser(result);
  }

  /**
   * 根据Google ID获取用户
   */
  async getUserByGoogleId(googleId: string): Promise<User | null> {
    const result = await this.db.prepare(`
      SELECT * FROM users WHERE google_id = ?
    `).bind(googleId).first();

    if (!result) return null;

    return this.mapDbUserToUser(result);
  }

  /**
   * 更新用户信息
   */
  async updateUser(userId: string, updates: Partial<User>): Promise<void> {
    const fields = [];
    const values = [];

    if (updates.name !== undefined) {
      fields.push('name = ?');
      values.push(updates.name);
    }
    if (updates.avatar !== undefined) {
      fields.push('avatar = ?');
      values.push(updates.avatar);
    }
    if (updates.credits !== undefined) {
      fields.push('credits = ?');
      values.push(updates.credits);
    }

    if (fields.length === 0) return;

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());
    values.push(userId);

    await this.db.prepare(`
      UPDATE users SET ${fields.join(', ')} WHERE id = ?
    `).bind(...values).run();
  }

  /**
   * 创建故事
   */
  async createStory(storyData: Omit<Story, 'id' | 'createdAt' | 'updatedAt'>): Promise<Story> {
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    await this.db.prepare(`
      INSERT INTO stories (
        id, user_id, title, character_name, character_age, character_traits,
        theme, setting, style, voice, pages, audio_url, cover_image_url,
        status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id,
      storyData.userId,
      storyData.title,
      storyData.characterName,
      storyData.characterAge,
      JSON.stringify(storyData.characterTraits),
      storyData.theme,
      storyData.setting,
      storyData.style,
      storyData.voice,
      JSON.stringify(storyData.pages),
      storyData.audioUrl || null,
      storyData.coverImageUrl || null,
      storyData.status,
      now,
      now
    ).run();

    return {
      id,
      ...storyData,
      createdAt: now,
      updatedAt: now
    };
  }

  /**
   * 获取故事
   */
  async getStoryById(storyId: string): Promise<Story | null> {
    const result = await this.db.prepare(`
      SELECT * FROM stories WHERE id = ?
    `).bind(storyId).first();

    if (!result) return null;

    return this.mapDbStoryToStory(result);
  }

  /**
   * 获取用户的故事列表
   */
  async getUserStories(userId: string, page: number = 1, limit: number = 10): Promise<{
    stories: Story[];
    total: number;
  }> {
    const offset = (page - 1) * limit;

    // 获取故事列表
    const stories = await this.db.prepare(`
      SELECT * FROM stories 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `).bind(userId, limit, offset).all();

    // 获取总数
    const countResult = await this.db.prepare(`
      SELECT COUNT(*) as count FROM stories WHERE user_id = ?
    `).bind(userId).first();

    return {
      stories: stories.results.map(this.mapDbStoryToStory),
      total: countResult?.count || 0
    };
  }

  /**
   * 更新故事
   */
  async updateStory(storyId: string, updates: Partial<Story>): Promise<void> {
    const fields = [];
    const values = [];

    if (updates.title !== undefined) {
      fields.push('title = ?');
      values.push(updates.title);
    }
    if (updates.pages !== undefined) {
      fields.push('pages = ?');
      values.push(JSON.stringify(updates.pages));
    }
    if (updates.audioUrl !== undefined) {
      fields.push('audio_url = ?');
      values.push(updates.audioUrl);
    }
    if (updates.coverImageUrl !== undefined) {
      fields.push('cover_image_url = ?');
      values.push(updates.coverImageUrl);
    }
    if (updates.status !== undefined) {
      fields.push('status = ?');
      values.push(updates.status);
    }

    if (fields.length === 0) return;

    fields.push('updated_at = ?');
    values.push(new Date().toISOString());
    values.push(storyId);

    await this.db.prepare(`
      UPDATE stories SET ${fields.join(', ')} WHERE id = ?
    `).bind(...values).run();
  }

  /**
   * 创建订阅
   */
  async createSubscription(subscriptionData: Omit<UserSubscription, 'id'>): Promise<UserSubscription> {
    const id = crypto.randomUUID();

    await this.db.prepare(`
      INSERT INTO subscriptions (
        id, user_id, plan, status, current_period_start,
        current_period_end, stripe_subscription_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id,
      subscriptionData.userId,
      subscriptionData.plan,
      subscriptionData.status,
      subscriptionData.currentPeriodStart,
      subscriptionData.currentPeriodEnd,
      subscriptionData.stripeSubscriptionId || null
    ).run();

    return {
      id,
      ...subscriptionData
    };
  }

  /**
   * 创建支付记录
   */
  async createPayment(paymentData: Omit<Payment, 'id' | 'createdAt' | 'updatedAt'>): Promise<Payment> {
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    await this.db.prepare(`
      INSERT INTO payments (
        id, user_id, type, amount, currency, status,
        stripe_payment_intent_id, stripe_subscription_id, metadata,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id,
      paymentData.userId,
      paymentData.type,
      paymentData.amount,
      paymentData.currency,
      paymentData.status,
      paymentData.stripePaymentIntentId || null,
      paymentData.stripeSubscriptionId || null,
      paymentData.metadata || null,
      now,
      now
    ).run();

    return {
      id,
      ...paymentData,
      createdAt: now,
      updatedAt: now
    };
  }

  /**
   * 获取用户订阅
   */
  async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    // 🔍 DEBUG: 首先查询所有订阅记录用于调试
    const allSubscriptions = await this.db.prepare(`
      SELECT * FROM subscriptions WHERE user_id = ? ORDER BY created_at DESC
    `).bind(userId).all();

    console.log(`🔍 用户 ${userId} 的所有订阅记录:`, {
      count: allSubscriptions.results?.length || 0,
      subscriptions: allSubscriptions.results?.map(sub => ({
        id: sub.id,
        plan: sub.plan,
        status: sub.status,
        created_at: sub.created_at,
        current_period_end: sub.current_period_end
      })) || []
    });

    // 查询活跃订阅
    const result = await this.db.prepare(`
      SELECT * FROM subscriptions WHERE user_id = ? AND status = 'active'
    `).bind(userId).first();

    if (!result) {
      console.log(`⚠️ 用户 ${userId} 没有活跃订阅，将显示为免费计划`);
      return null;
    }

    console.log(`✅ 用户 ${userId} 的活跃订阅:`, {
      plan: result.plan,
      status: result.status,
      current_period_end: result.current_period_end
    });

    return this.mapDbSubscriptionToSubscription(result);
  }

  // ==================== 辅助方法 ====================

  private mapDbUserToUser(dbUser: any): User {
    return {
      id: dbUser.id,
      email: dbUser.email,
      name: dbUser.name,
      avatar: dbUser.avatar,
      googleId: dbUser.google_id,
      credits: dbUser.credits,
      createdAt: dbUser.created_at,
      updatedAt: dbUser.updated_at
    };
  }

  private mapDbStoryToStory(dbStory: any): Story {
    return {
      id: dbStory.id,
      userId: dbStory.user_id,
      title: dbStory.title,
      characterName: dbStory.character_name,
      characterAge: dbStory.character_age,
      characterTraits: JSON.parse(dbStory.character_traits),
      theme: dbStory.theme,
      setting: dbStory.setting,
      style: dbStory.style,
      voice: dbStory.voice,
      pages: JSON.parse(dbStory.pages),
      audioUrl: dbStory.audio_url,
      coverImageUrl: dbStory.cover_image_url,
      status: dbStory.status,
      createdAt: dbStory.created_at,
      updatedAt: dbStory.updated_at
    };
  }

  private mapDbSubscriptionToSubscription(dbSub: any): UserSubscription {
    return {
      id: dbSub.id,
      userId: dbSub.user_id,
      plan: dbSub.plan,
      status: dbSub.status,
      currentPeriodStart: dbSub.current_period_start,
      currentPeriodEnd: dbSub.current_period_end,
      stripeSubscriptionId: dbSub.stripe_subscription_id
    };
  }
}