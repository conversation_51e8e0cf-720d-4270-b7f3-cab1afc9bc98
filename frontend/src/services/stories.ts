import { api } from './api';
import { 
  Story, 
  CreateStoryRequest, 
  StoryGenerationProgress,
  StoryFilters,
  PaginatedResponse
} from '@/types';

class StoryService {
  /**
   * Create a new story
   */
  async createStory(request: CreateStoryRequest): Promise<Story> {
    const response = await api.post<{storyId: string; status: any}>('/stories', request);

    // Backend returns {storyId, status}, but we need to return a Story object
    // Create a minimal Story object with the returned storyId
    const story: Story = {
      id: response.storyId,
      title: `${request.characterName}的故事`, // Temporary title
      characterName: request.characterName,
      characterAge: request.characterAge,
      characterTraits: request.characterTraits,
      theme: request.theme,
      setting: request.setting,
      style: request.style,
      voice: request.voice,
      pages: [],
      status: 'preparing',
      userId: '', // Will be set by backend
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return story;
  }

  /**
   * Get user's stories with pagination and filters
   */
  async getStories(params?: StoryFilters): Promise<PaginatedResponse<Story>> {
    const searchParams = new URLSearchParams();

    if (params?.query) searchParams.append('query', params.query);
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.status) searchParams.append('status', params.status);
    if (params?.theme) searchParams.append('theme', params.theme);
    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);
    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);
    if (params?.sortBy) {
      searchParams.append('sortBy', params.sortBy);
      searchParams.append('sortOrder', params.sortOrder || 'desc');
    }

    const queryString = searchParams.toString();
    const url = queryString ? `/stories?${queryString}` : '/stories';

    return api.get<PaginatedResponse<Story>>(url);
  }

  /**
   * Get a specific story by ID
   */
  async getStoryById(id: string): Promise<Story> {
    return api.get<Story>(`/stories/${id}`);
  }

  /**
   * Update a story
   */
  async updateStory(id: string, updates: Partial<Story>): Promise<Story> {
    return api.put<Story>(`/stories/${id}`, updates);
  }

  /**
   * Delete a story
   */
  async deleteStory(id: string): Promise<void> {
    return api.delete(`/stories/${id}`);
  }

  /**
   * Get story generation status
   */
  async getGenerationStatus(storyId: string): Promise<StoryGenerationProgress> {
    return api.get<StoryGenerationProgress>(`/stories/${storyId}/status`);
  }

  /**
   * Cancel story generation
   */
  async cancelGeneration(storyId: string): Promise<void> {
    return api.post(`/stories/${storyId}/cancel`);
  }

  /**
   * Regenerate story content
   */
  async regenerateStory(storyId: string, options?: {
    regenerateText?: boolean;
    regenerateImages?: boolean;
    regenerateAudio?: boolean;
  }): Promise<Story> {
    return api.post<Story>(`/stories/${storyId}/regenerate`, options);
  }

  /**
   * Get story themes
   */
  async getThemes(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    icon: string;
    ageGroups: number[];
    popular: boolean;
  }>> {
    return api.get('/stories/themes');
  }

  /**
   * Get story styles
   */
  async getStyles(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    preview: string;
    ageGroups: number[];
  }>> {
    return api.get('/stories/styles');
  }

  /**
   * Get story voices
   */
  async getVoices(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    gender: 'male' | 'female' | 'neutral';
    accent: string;
    ageGroups: number[];
    sample?: string;
  }>> {
    return api.get('/stories/voices');
  }

  /**
   * Get story settings
   */
  async getSettings(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    icon: string;
    popular: boolean;
  }>> {
    return api.get('/stories/settings');
  }

  /**
   * Get character traits
   */
  async getCharacterTraits(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    category: 'personality' | 'appearance' | 'ability' | 'interest';
    icon: string;
  }>> {
    return api.get('/stories/traits');
  }

  /**
   * Share a story
   */
  async shareStory(storyId: string, options: any): Promise<{
    shareUrl: string;
    expiresAt: string;
  }> {
    return api.post(`/stories/${storyId}/share`, options);
  }

  /**
   * Export a story
   */
  async exportStory(storyId: string, options: any): Promise<any> {
    return api.post(`/stories/${storyId}/export`, options);
  }

  /**
   * Get story export status
   */
  async getExportStatus(exportId: string): Promise<any> {
    return api.get(`/stories/exports/${exportId}`);
  }

  /**
   * Rate a story
   */
  async rateStory(storyId: string, rating: number, comment?: string): Promise<void> {
    return api.post(`/stories/${storyId}/rate`, { rating, comment });
  }

  /**
   * Get story metrics
   */
  async getStoryMetrics(storyId: string): Promise<{
    views: number;
    plays: number;
    shares: number;
    averageRating?: number;
    totalRatings: number;
    lastViewedAt?: string;
  }> {
    return api.get(`/stories/${storyId}/metrics`);
  }

  /**
   * Get story templates
   */
  async getTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    preview: string;
    settings: Partial<CreateStoryRequest>;
    category: string;
    popular: boolean;
    ageGroups: number[];
  }>> {
    return api.get('/stories/templates');
  }

  /**
   * Create story from template
   */
  async createFromTemplate(templateId: string, customizations?: Partial<CreateStoryRequest>): Promise<Story> {
    return api.post<Story>(`/stories/templates/${templateId}`, customizations);
  }

  /**
   * Get story recommendations
   */
  async getRecommendations(limit = 6): Promise<Array<{
    id: string;
    name: string;
    description: string;
    settings: Partial<CreateStoryRequest>;
    reason: string;
  }>> {
    return api.get(`/stories/recommendations?limit=${limit}`);
  }

  /**
   * Search public stories (if we add this feature)
   */
  async searchPublicStories(query: string, page = 1, limit = 12): Promise<PaginatedResponse<Story>> {
    return api.get(`/stories/public/search?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`);
  }

  /**
   * Get trending stories
   */
  async getTrendingStories(limit = 12): Promise<Story[]> {
    return api.get(`/stories/trending?limit=${limit}`);
  }

  /**
   * Duplicate a story
   */
  async duplicateStory(storyId: string, title?: string): Promise<Story> {
    return api.post<Story>(`/stories/${storyId}/duplicate`, { title });
  }

  /**
   * Archive a story
   */
  async archiveStory(storyId: string): Promise<void> {
    return api.post(`/stories/${storyId}/archive`);
  }

  /**
   * Unarchive a story
   */
  async unarchiveStory(storyId: string): Promise<void> {
    return api.post(`/stories/${storyId}/unarchive`);
  }
}

// Create and export service instance
export const storyService = new StoryService();

// WebSocket connection for real-time story generation updates
export class StoryGenerationSocket {
  private ws: WebSocket | null = null;
  private storyId: string;
  private onProgress: (progress: StoryGenerationProgress) => void;
  private onComplete: (story: Story) => void;
  private onError: (error: string) => void;

  constructor(
    storyId: string,
    onProgress: (progress: StoryGenerationProgress) => void,
    onComplete: (story: Story) => void,
    onError: (error: string) => void
  ) {
    this.storyId = storyId;
    this.onProgress = onProgress;
    this.onComplete = onComplete;
    this.onError = onError;
  }

  connect() {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8787/ws';
    this.ws = new WebSocket(`${wsUrl}/stories/${this.storyId}/generation`);

    this.ws.onopen = () => {
      console.log('Story generation WebSocket connected');
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
          case 'progress':
            this.onProgress(data.payload);
            break;
          case 'complete':
            this.onComplete(data.payload);
            this.disconnect();
            break;
          case 'error':
            this.onError(data.payload.message);
            this.disconnect();
            break;
        }
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onerror = (error) => {
      console.error('Story generation WebSocket error:', error);
      this.onError('Connection error occurred');
    };

    this.ws.onclose = () => {
      console.log('Story generation WebSocket disconnected');
    };
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
