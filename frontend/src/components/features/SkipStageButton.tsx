import React, { useState, useEffect } from 'react';
import { Clock, SkipForward, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Modal } from '@/components/ui/Modal';

interface SkipStageButtonProps {
  storyId: string;
  currentStage: 'text' | 'image' | 'audio';
  stageStartTime: number;
  onSkip: (stage: string, reason: string) => Promise<void>;
  className?: string;
}

interface StageConfig {
  name: string;
  timeoutMinutes: number;
  description: string;
  skipImpact: string;
}

const STAGE_CONFIGS: Record<string, StageConfig> = {
  text: {
    name: '文本生成',
    timeoutMinutes: 2,
    description: '正在生成故事文本内容',
    skipImpact: '将使用预设的故事模板，内容可能较为简单'
  },
  image: {
    name: '图片生成',
    timeoutMinutes: 5,
    description: '正在生成故事插图',
    skipImpact: '将使用占位符图片，故事仍可正常阅读'
  },
  audio: {
    name: '音频生成',
    timeoutMinutes: 3,
    description: '正在生成语音朗读',
    skipImpact: '将跳过语音功能，故事仍可正常阅读'
  }
};

export const SkipStageButton: React.FC<SkipStageButtonProps> = ({
  storyId,
  currentStage,
  stageStartTime,
  onSkip,
  className = ''
}) => {
  const [elapsedMinutes, setElapsedMinutes] = useState(0);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isSkipping, setIsSkipping] = useState(false);

  const stageConfig = STAGE_CONFIGS[currentStage];

  // 计算已经过的时间
  useEffect(() => {
    const updateElapsedTime = () => {
      const now = Date.now();
      const elapsed = Math.floor((now - stageStartTime) / (1000 * 60));
      setElapsedMinutes(elapsed);
    };

    updateElapsedTime();
    const interval = setInterval(updateElapsedTime, 10000); // 每10秒更新一次

    return () => clearInterval(interval);
  }, [stageStartTime]);

  // 判断是否应该显示跳过按钮
  const shouldShowSkipButton = elapsedMinutes >= stageConfig.timeoutMinutes;

  const handleSkipClick = () => {
    setShowConfirmModal(true);
  };

  const handleConfirmSkip = async () => {
    setIsSkipping(true);
    try {
      await onSkip(currentStage, `用户手动跳过 - 等待时间超过${elapsedMinutes}分钟`);
      setShowConfirmModal(false);
    } catch (error) {
      console.error('跳过阶段失败:', error);
    } finally {
      setIsSkipping(false);
    }
  };

  const handleCancelSkip = () => {
    setShowConfirmModal(false);
  };

  if (!shouldShowSkipButton) {
    return (
      <div className={`flex items-center text-sm text-gray-500 ${className}`}>
        <Clock className="w-4 h-4 mr-2" />
        <span>
          {stageConfig.description} ({elapsedMinutes}/{stageConfig.timeoutMinutes}分钟)
        </span>
      </div>
    );
  }

  return (
    <>
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="flex items-center text-sm text-amber-600">
          <AlertTriangle className="w-4 h-4 mr-2" />
          <span>
            {stageConfig.name}已运行{elapsedMinutes}分钟
          </span>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleSkipClick}
          disabled={isSkipping}
          className="flex items-center space-x-2 text-orange-600 border-orange-300 hover:bg-orange-50"
        >
          <SkipForward className="w-4 h-4" />
          <span>{isSkipping ? '跳过中...' : '跳过此阶段'}</span>
        </Button>
      </div>

      {/* 确认跳过模态框 */}
      <Modal
        isOpen={showConfirmModal}
        onClose={handleCancelSkip}
        title="确认跳过阶段"
        size="md"
      >
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-6 h-6 text-amber-500 mt-1 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                跳过{stageConfig.name}阶段
              </h3>
              <p className="text-gray-600 mb-3">
                您即将跳过{stageConfig.name}阶段。这个操作无法撤销。
              </p>
              
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-4">
                <h4 className="font-medium text-amber-800 mb-1">跳过影响：</h4>
                <p className="text-sm text-amber-700">
                  {stageConfig.skipImpact}
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h4 className="font-medium text-blue-800 mb-1">替代方案：</h4>
                <p className="text-sm text-blue-700">
                  您可以稍后在故事详情页面选择"重新生成"来补充跳过的内容。
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={handleCancelSkip}
              disabled={isSkipping}
            >
              继续等待
            </Button>
            <Button
              variant="primary"
              onClick={handleConfirmSkip}
              disabled={isSkipping}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isSkipping ? '跳过中...' : '确认跳过'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default SkipStageButton;
