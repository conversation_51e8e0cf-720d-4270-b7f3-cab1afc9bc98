import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';

interface PageAudioPlayerProps {
  audioUrl: string;
  pageNumber: number;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
}

export const PageAudioPlayer: React.FC<PageAudioPlayerProps> = ({
  audioUrl,
  pageNumber,
  className = '',
  size = 'medium',
  onPlay,
  onPause,
  onEnded
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadStart = () => {
      setIsLoading(true);
      setError(null);
    };

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      setIsLoading(false);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handlePlay = () => {
      setIsPlaying(true);
      onPlay?.();
    };

    const handlePause = () => {
      setIsPlaying(false);
      onPause?.();
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      onEnded?.();
    };

    const handleError = () => {
      setIsLoading(false);
      setError('音频加载失败');
      setIsPlaying(false);
    };

    const handleCanPlay = () => {
      setIsLoading(false);
    };

    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);
    audio.addEventListener('canplay', handleCanPlay);

    return () => {
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('canplay', handleCanPlay);
    };
  }, [onPlay, onPause, onEnded]);

  const togglePlay = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      if (isPlaying) {
        audio.pause();
      } else {
        await audio.play();
      }
    } catch (error) {
      console.error('Audio playback error:', error);
      setError('播放失败');
    }
  };

  const formatTime = (time: number): string => {
    if (isNaN(time)) return '0:00';
    
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          button: 'w-8 h-8',
          icon: 'w-3 h-3',
          text: 'text-xs',
          container: 'space-x-2'
        };
      case 'large':
        return {
          button: 'w-12 h-12',
          icon: 'w-6 h-6',
          text: 'text-sm',
          container: 'space-x-4'
        };
      default: // medium
        return {
          button: 'w-10 h-10',
          icon: 'w-4 h-4',
          text: 'text-sm',
          container: 'space-x-3'
        };
    }
  };

  const sizeClasses = getSizeClasses();
  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  if (error) {
    return (
      <div className={`flex items-center space-x-2 text-red-500 ${className}`}>
        <VolumeX className={sizeClasses.icon} />
        <span className={sizeClasses.text}>播放失败</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center ${sizeClasses.container} ${className}`}>
      <audio
        ref={audioRef}
        src={audioUrl}
        preload="metadata"
      />
      
      {/* 播放/暂停按钮 */}
      <button
        onClick={togglePlay}
        disabled={isLoading}
        className={`
          flex items-center justify-center ${sizeClasses.button}
          bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 
          text-white rounded-full transition-colors shadow-sm
        `}
        title={isPlaying ? '暂停' : '播放'}
      >
        {isLoading ? (
          <div className={`${sizeClasses.icon} border-2 border-white border-t-transparent rounded-full animate-spin`} />
        ) : isPlaying ? (
          <Pause className={sizeClasses.icon} />
        ) : (
          <Play className={`${sizeClasses.icon} ml-0.5`} />
        )}
      </button>
      
      {/* 页面信息和进度 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <span className={`${sizeClasses.text} font-medium text-gray-900`}>
            第{pageNumber}页
          </span>
          {duration > 0 && (
            <span className={`${sizeClasses.text} text-gray-500`}>
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
          )}
        </div>
        
        {/* 进度条 */}
        {duration > 0 && (
          <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-blue-600 transition-all duration-150"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        )}
      </div>
      
      {/* 音量图标 */}
      <Volume2 className={`${sizeClasses.icon} text-gray-400`} />
    </div>
  );
};

// 简化版本的页面音频按钮
interface SimplePageAudioButtonProps {
  audioUrl: string;
  pageNumber: number;
  className?: string;
  onPlay?: () => void;
  onPause?: () => void;
}

export const SimplePageAudioButton: React.FC<SimplePageAudioButtonProps> = ({
  audioUrl,
  pageNumber,
  className = '',
  onPlay,
  onPause
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    const handlePlay = () => {
      setIsPlaying(true);
      onPlay?.();
    };
    const handlePause = () => {
      setIsPlaying(false);
      onPause?.();
    };
    const handleEnded = () => {
      setIsPlaying(false);
    };
    const handleError = () => {
      setIsLoading(false);
      setError('加载失败');
      setIsPlaying(false);
    };

    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
    };
  }, [onPlay, onPause]);

  const togglePlay = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      if (isPlaying) {
        audio.pause();
      } else {
        await audio.play();
      }
    } catch (error) {
      console.error('Audio playback error:', error);
      setError('播放失败');
    }
  };

  if (error) {
    return (
      <button
        disabled
        className={`p-2 text-red-500 bg-red-50 rounded-full ${className}`}
        title="音频加载失败"
      >
        <VolumeX className="w-4 h-4" />
      </button>
    );
  }

  return (
    <>
      <audio
        ref={audioRef}
        src={audioUrl}
        preload="metadata"
      />
      
      <button
        onClick={togglePlay}
        disabled={isLoading}
        className={`
          p-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 
          text-white rounded-full transition-colors shadow-sm
          ${className}
        `}
        title={isPlaying ? `暂停第${pageNumber}页朗读` : `播放第${pageNumber}页朗读`}
      >
        {isLoading ? (
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
        ) : isPlaying ? (
          <Pause className="w-4 h-4" />
        ) : (
          <Play className="w-4 h-4 ml-0.5" />
        )}
      </button>
    </>
  );
};
