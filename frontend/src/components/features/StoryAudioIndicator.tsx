import React from 'react';
import { Volume2, VolumeX, Plus } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Tooltip } from '@/components/ui/Tooltip';
import type { Story } from '@/types/story';

interface StoryAudioIndicatorProps {
  story: Story;
  size?: 'sm' | 'md' | 'lg';
  showAddOption?: boolean;
  onAddAudio?: () => void;
  className?: string;
}

export const StoryAudioIndicator: React.FC<StoryAudioIndicatorProps> = ({
  story,
  size = 'md',
  showAddOption = false,
  onAddAudio,
  className = ''
}) => {
  const hasAudio = !!story.audioUrl;
  
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const iconSize = sizeClasses[size];

  if (hasAudio) {
    return (
      <Tooltip content="此故事包含语音朗读">
        <div className={`inline-flex items-center space-x-1 ${className}`}>
          <Volume2 className={`${iconSize} text-green-600`} />
          {size !== 'sm' && (
            <span className="text-sm text-green-600 font-medium">有声</span>
          )}
        </div>
      </Tooltip>
    );
  }

  if (showAddOption && onAddAudio) {
    return (
      <div className={`inline-flex items-center space-x-2 ${className}`}>
        <Tooltip content="此故事暂无语音朗读">
          <div className="inline-flex items-center space-x-1">
            <VolumeX className={`${iconSize} text-gray-400`} />
            {size !== 'sm' && (
              <span className="text-sm text-gray-400">无声</span>
            )}
          </div>
        </Tooltip>
        
        <Button
          variant="outline"
          size="sm"
          onClick={onAddAudio}
          className="text-blue-600 border-blue-300 hover:bg-blue-50"
        >
          <Plus className="w-3 h-3 mr-1" />
          添加语音
        </Button>
      </div>
    );
  }

  return (
    <Tooltip content="此故事暂无语音朗读">
      <div className={`inline-flex items-center space-x-1 ${className}`}>
        <VolumeX className={`${iconSize} text-gray-400`} />
        {size !== 'sm' && (
          <span className="text-sm text-gray-400">无声</span>
        )}
      </div>
    </Tooltip>
  );
};

// 列表项中的简化版本
export const StoryAudioBadge: React.FC<{ story: Story; className?: string }> = ({
  story,
  className = ''
}) => {
  const hasAudio = !!story.audioUrl;
  
  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        hasAudio
          ? 'bg-green-100 text-green-800'
          : 'bg-gray-100 text-gray-600'
      } ${className}`}
    >
      {hasAudio ? (
        <>
          <Volume2 className="w-3 h-3 mr-1" />
          有声
        </>
      ) : (
        <>
          <VolumeX className="w-3 h-3 mr-1" />
          无声
        </>
      )}
    </span>
  );
};

export default StoryAudioIndicator;
