import React, { useState } from 'react';
import { StoryGenerationProgress } from '@/types/story';

interface EnhancedProgressDisplayProps {
  progress: StoryGenerationProgress;
  className?: string;
  onPreviewContent?: (type: 'text' | 'image' | 'audio', taskId: string) => void;
}

export const EnhancedProgressDisplay: React.FC<EnhancedProgressDisplayProps> = ({
  progress,
  className = '',
  onPreviewContent
}) => {
  const { taskDetails, currentStep, stageProgress } = progress;

  // 如果有详细任务信息，显示增强版本
  if (taskDetails && taskDetails.length > 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">故事生成进度</h3>
          <p className="text-sm text-gray-600">{currentStep}</p>
        </div>

        <div className="space-y-4">
          {taskDetails.map((task) => (
            <TaskProgressItem
              key={task.id}
              task={task}
              onPreviewContent={onPreviewContent}
            />
          ))}
        </div>

        <div className="mt-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>总体进度</span>
            <span>{progress.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress.progress}%` }}
            />
          </div>
        </div>
      </div>
    );
  }

  // 降级到传统显示
  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">故事生成进度</h3>
        <p className="text-sm text-gray-600">{currentStep}</p>
      </div>

      <div className="space-y-3">
        <StageProgressItem
          name="准备生成"
          completed={stageProgress.preparing}
          current={progress.stage === 'preparing'}
        />
        <StageProgressItem
          name="创作文本"
          completed={stageProgress.generating_text}
          current={progress.stage === 'generating_text'}
        />
        <StageProgressItem
          name="绘制插图"
          completed={stageProgress.generating_images}
          current={progress.stage === 'generating_images'}
        />
        <StageProgressItem
          name="合成语音"
          completed={stageProgress.generating_audio}
          current={progress.stage === 'generating_audio'}
        />
        <StageProgressItem
          name="最终合成"
          completed={stageProgress.composing}
          current={progress.stage === 'composing'}
        />
      </div>
    </div>
  );
};

interface TaskProgressItemProps {
  task: {
    id: string;
    type: 'text' | 'image' | 'audio';
    status: 'pending' | 'running' | 'completed' | 'failed';
    progress: number;
    error?: string;
  };
  onPreviewContent?: (type: 'text' | 'image' | 'audio', taskId: string) => void;
}

const TaskProgressItem: React.FC<TaskProgressItemProps> = ({ task, onPreviewContent }) => {
  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'text':
        return '📝';
      case 'image':
        return '🎨';
      case 'audio':
        return '🎵';
      default:
        return '⚙️';
    }
  };

  const getPreviewIcon = (type: string) => {
    switch (type) {
      case 'text':
        return '📖';
      case 'image':
        return '🖼️';
      case 'audio':
        return '🎧';
      default:
        return '👁️';
    }
  };

  const getTaskName = (type: string) => {
    switch (type) {
      case 'text':
        return '故事文本';
      case 'image':
        return '故事插图';
      case 'audio':
        return '语音旁白';
      default:
        return '未知任务';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'running':
        return 'text-blue-600';
      case 'failed':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'running':
        return '进行中';
      case 'failed':
        return '失败';
      case 'pending':
        return '等待中';
      default:
        return '未知';
    }
  };

  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div className="flex items-center space-x-3">
        <span className="text-xl">{getTaskIcon(task.type)}</span>
        <div>
          <div className="font-medium text-gray-900">{getTaskName(task.type)}</div>
          <div className={`text-sm ${getStatusColor(task.status)}`}>
            {getStatusText(task.status)}
            {task.error && (
              <span className="text-red-500 ml-2">- {task.error}</span>
            )}
          </div>
        </div>
      </div>
      
      <div className="flex items-center space-x-3">
        {task.status === 'running' && (
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
            <span className="text-sm text-gray-600">{task.progress}%</span>
          </div>
        )}
        {task.status === 'completed' && (
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>

            {/* 预览按钮 */}
            {onPreviewContent && (
              <button
                onClick={() => onPreviewContent(task.type, task.id)}
                className="ml-2 px-3 py-1 text-xs bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-full transition-colors flex items-center space-x-1"
              >
                <span>{getPreviewIcon(task.type)}</span>
                <span>预览</span>
              </button>
            )}
          </div>
        )}
        {task.status === 'failed' && (
          <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        )}
      </div>
    </div>
  );
};

interface StageProgressItemProps {
  name: string;
  completed: boolean;
  current: boolean;
}

const StageProgressItem: React.FC<StageProgressItemProps> = ({
  name,
  completed,
  current
}) => {
  return (
    <div className="flex items-center space-x-3">
      <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
        completed 
          ? 'bg-green-100 text-green-600' 
          : current 
            ? 'bg-blue-100 text-blue-600' 
            : 'bg-gray-100 text-gray-400'
      }`}>
        {completed ? (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        ) : current ? (
          <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
        ) : (
          <div className="w-2 h-2 bg-current rounded-full" />
        )}
      </div>
      <span className={`text-sm ${
        completed 
          ? 'text-green-600 font-medium' 
          : current 
            ? 'text-blue-600 font-medium' 
            : 'text-gray-500'
      }`}>
        {name}
      </span>
    </div>
  );
};
