import React, { useState, useEffect } from 'react';
import { Volume2, VolumeX, Info, Coins } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Switch } from '@/components/ui/Switch';
import { Tooltip } from '@/components/ui/Tooltip';
import { useAuthStore } from '@/stores/authStore';
import type { CreateStoryRequest } from '@/types/story';

interface AudioOptionsProps {
  data: Partial<CreateStoryRequest>;
  onChange: (updates: Partial<CreateStoryRequest>) => void;
  onValidationChange?: (isValid: boolean) => void;
}

// 积分消耗配置
const CREDIT_COSTS = {
  base: 20,        // 基础故事（文本+图片）
  audio: 10,       // 音频生成额外消耗
  total: 30        // 完整故事总消耗
};

export const AudioOptions: React.FC<AudioOptionsProps> = ({
  data,
  onChange,
  onValidationChange
}) => {
  const { user } = useAuthStore();
  const [enableAudio, setEnableAudio] = useState(!data.skipAudio);

  // 计算积分消耗
  const calculateCredits = () => {
    return enableAudio ? CREDIT_COSTS.total : CREDIT_COSTS.base;
  };

  // 检查用户积分是否足够
  const hasEnoughCredits = () => {
    const requiredCredits = calculateCredits();
    return !user || user.credits >= requiredCredits;
  };

  // 处理音频开关变化
  const handleAudioToggle = (enabled: boolean) => {
    setEnableAudio(enabled);
    onChange({
      skipAudio: !enabled,
      // 如果禁用音频，清除voice选择
      voice: enabled ? data.voice || 'gentle_female' : undefined
    });
  };

  // 处理语音类型选择
  const handleVoiceChange = (voice: string) => {
    onChange({ voice });
  };

  // 验证状态
  useEffect(() => {
    const isValid = enableAudio ? !!data.voice : true;
    onValidationChange?.(isValid);
  }, [enableAudio, data.voice, onValidationChange]);

  const requiredCredits = calculateCredits();
  const creditsSufficient = hasEnoughCredits();

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">语音配置</h2>
        <p className="text-gray-600">选择是否为故事添加语音朗读功能</p>
      </div>

      {/* 积分消耗提示 */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="flex items-center space-x-3">
          <Coins className="w-5 h-5 text-blue-600" />
          <div className="flex-1">
            <div className="font-medium text-blue-900">积分消耗说明</div>
            <div className="text-sm text-blue-700 mt-1">
              基础故事（文本+图片）：{CREDIT_COSTS.base} 积分
              {enableAudio && (
                <span className="block">语音朗读功能：+{CREDIT_COSTS.audio} 积分</span>
              )}
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-blue-900">
              {requiredCredits} 积分
            </div>
            {user && (
              <div className={`text-sm ${creditsSufficient ? 'text-green-600' : 'text-red-600'}`}>
                您有 {user.credits} 积分
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* 语音开关 */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`p-3 rounded-full ${enableAudio ? 'bg-green-100' : 'bg-gray-100'}`}>
              {enableAudio ? (
                <Volume2 className={`w-6 h-6 ${enableAudio ? 'text-green-600' : 'text-gray-400'}`} />
              ) : (
                <VolumeX className="w-6 h-6 text-gray-400" />
              )}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                生成语音朗读
              </h3>
              <p className="text-sm text-gray-600">
                为故事添加专业的语音朗读，提升阅读体验
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <Tooltip content={enableAudio ? "关闭语音功能可节省10积分" : "开启语音功能需额外消耗10积分"}>
              <Info className="w-4 h-4 text-gray-400" />
            </Tooltip>
            <Switch
              checked={enableAudio}
              onCheckedChange={handleAudioToggle}
              disabled={!creditsSufficient && !enableAudio}
            />
          </div>
        </div>

        {/* 积分不足警告 */}
        {!creditsSufficient && enableAudio && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <Info className="w-4 h-4 text-red-600" />
              <span className="text-sm text-red-700">
                积分不足！开启语音功能需要 {requiredCredits} 积分，您当前有 {user?.credits || 0} 积分。
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 text-red-600 border-red-300 hover:bg-red-50"
              onClick={() => window.open('/pricing', '_blank')}
            >
              购买积分
            </Button>
          </div>
        )}
      </Card>

      {/* 语音类型选择 */}
      {enableAudio && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">选择语音类型</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              {
                id: 'gentle_female',
                name: '温柔女声',
                description: '适合温馨、童话类故事',
                icon: '👩'
              },
              {
                id: 'warm_male',
                name: '温暖男声',
                description: '适合冒险、成长类故事',
                icon: '👨'
              },
              {
                id: 'child_friendly',
                name: '儿童友好',
                description: '活泼可爱，适合低龄儿童',
                icon: '🧒'
              },
              {
                id: 'storyteller',
                name: '故事讲述者',
                description: '专业讲故事语调',
                icon: '📚'
              }
            ].map((voice) => (
              <button
                key={voice.id}
                onClick={() => handleVoiceChange(voice.id)}
                className={`p-4 rounded-lg border-2 transition-all text-left ${
                  data.voice === voice.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{voice.icon}</span>
                  <div>
                    <div className="font-medium text-gray-900">{voice.name}</div>
                    <div className="text-sm text-gray-600">{voice.description}</div>
                  </div>
                </div>
              </button>
            ))}
          </div>

          {/* 语音预览 */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-sm text-gray-600">
              💡 提示：语音将在故事生成完成后可用，您可以在故事详情页面播放完整的语音朗读。
            </div>
          </div>
        </Card>
      )}

      {/* 跳过音频的说明 */}
      {!enableAudio && (
        <Card className="p-6 bg-gray-50">
          <div className="text-center">
            <VolumeX className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              已跳过语音生成
            </h3>
            <p className="text-gray-600 mb-4">
              您的故事将包含文本和图片，但不包含语音朗读功能。
              这将为您节省 {CREDIT_COSTS.audio} 积分。
            </p>
            <div className="text-sm text-gray-500">
              💡 您可以在故事创建完成后，在故事详情页面选择"添加语音"来补充语音功能。
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default AudioOptions;
