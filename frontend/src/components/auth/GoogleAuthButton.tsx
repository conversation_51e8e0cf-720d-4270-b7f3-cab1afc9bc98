import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/stores/authStore';
import { useNotifications } from '@/stores/uiStore';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface GoogleAuthButtonProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  className?: string;
}

export const GoogleAuthButton: React.FC<GoogleAuthButtonProps> = ({
  onSuccess,
  onError,
  disabled = false,
  className = '',
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoaded, setIsGoogleLoaded] = useState(false);
  const buttonRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const { login } = useAuthStore();
  const { showError, showSuccess } = useNotifications();

  console.log('🔧 GoogleAuthButton rendered, isGoogleLoaded:', isGoogleLoaded);

  // 事件处理函数
  const handleCredentialResponse = useCallback(async (response: { credential?: string }) => {
    setIsLoading(true);

    try {
      await login({ googleToken: response.credential });
      showSuccess('登录成功', '欢迎回到 StoryWeaver！');
      onSuccess?.();
      navigate('/');
    } catch (error) {
      console.error('Login failed:', error);
      showError('登录失败', '请检查网络连接后重试');
      onError?.('登录失败');
    } finally {
      setIsLoading(false);
    }
  }, [login, showSuccess, showError, onSuccess, navigate, onError]);

  const handleGoogleSuccess = useCallback((event: Event) => {
    const customEvent = event as CustomEvent<{ credential?: string }>;
    handleCredentialResponse(customEvent.detail);
  }, [handleCredentialResponse]);

  const handleGoogleError = useCallback((event: Event) => {
    const customEvent = event as CustomEvent<{ message?: string }>;
    const errorMessage = customEvent.detail?.message || '认证过程中发生错误';
    showError('认证失败', errorMessage);
    onError?.(errorMessage);
    setIsLoading(false);
  }, [showError, onError]);

  const initializeGoogle = useCallback(() => {
    if (!window.google?.accounts?.id) return;

    const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID || 'placeholder_google_client_id';
    console.log('🔧 Google Client ID:', clientId);
    console.log('🔧 Environment:', import.meta.env.MODE);

    try {
      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: handleCredentialResponse,
        auto_select: false,
        cancel_on_tap_outside: true,
        use_fedcm_for_prompt: false,
        itp_support: true,
      });

      // Render the button
      if (buttonRef.current) {
        console.log('🔧 Rendering Google button to element:', buttonRef.current);
        window.google.accounts.id.renderButton(buttonRef.current, {
          theme: 'outline',
          size: 'large',
          text: 'signin_with',
          shape: 'rectangular',
          logo_alignment: 'left',
          width: '100%',
        });
        console.log('🔧 Google button rendered successfully');
      } else {
        console.log('🔧 Button ref is null, cannot render Google button');
      }

      // Add event listeners for custom events
      window.addEventListener('google-auth-success', handleGoogleSuccess as EventListener);
      window.addEventListener('google-auth-error', handleGoogleError as EventListener);
    } catch (error) {
      console.error('Failed to initialize Google Auth:', error);
      showError('认证初始化失败', '请刷新页面重试');
    }
  }, [handleCredentialResponse, handleGoogleSuccess, handleGoogleError, showError]);

  useEffect(() => {
    // Load Google Identity Services script
    const loadGoogleScript = () => {
      if (window.google?.accounts?.id) {
        setIsGoogleLoaded(true);
        // Delay initialization to ensure DOM is ready
        setTimeout(() => {
          initializeGoogle();
        }, 100);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      
      script.onload = () => {
        setIsGoogleLoaded(true);
        // Delay initialization to ensure DOM is ready
        setTimeout(() => {
          initializeGoogle();
        }, 100);
      };
      
      script.onerror = () => {
        console.error('Failed to load Google Identity Services');
        showError('认证服务加载失败', '请检查网络连接后重试');
      };
      
      document.head.appendChild(script);
    };

    loadGoogleScript();

    // Cleanup
    return () => {
      // Remove event listeners
      window.removeEventListener('google-auth-success', handleGoogleSuccess as EventListener);
      window.removeEventListener('google-auth-error', handleGoogleError as EventListener);
    };
  }, [handleGoogleSuccess, handleGoogleError, initializeGoogle, showError]);

  // Re-render button when Google is loaded and DOM is ready
  useEffect(() => {
    if (isGoogleLoaded && buttonRef.current && window.google?.accounts?.id) {
      console.log('🔧 Re-attempting to render Google button after state change');
      setTimeout(() => {
        initializeGoogle();
      }, 100);
    }
  }, [isGoogleLoaded, initializeGoogle]);

  const handleManualSignIn = () => {
    if (!window.google?.accounts?.id) {
      showError('认证服务未就绪', '请稍后重试');
      return;
    }

    setIsLoading(true);
    window.google.accounts.id.prompt();
  };

  // Fallback button for when Google button doesn't load
  if (!isGoogleLoaded) {
    return (
      <Button
        onClick={handleManualSignIn}
        disabled={disabled || isLoading}
        className={`w-full ${className}`}
        variant="outline"
      >
        {isLoading ? (
          <LoadingSpinner size="sm" className="mr-2" />
        ) : (
          <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="currentColor"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="currentColor"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="currentColor"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
        )}
        使用 Google 登录
      </Button>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
          <LoadingSpinner size="sm" />
        </div>
      )}
      <div 
        ref={buttonRef} 
        className={disabled ? 'pointer-events-none opacity-50' : ''}
      />
    </div>
  );
};

// Hook for Google Auth state
export const useGoogleAuth = () => {
  const [isReady, setIsReady] = useState(false);
  const { isAuthenticated, isLoading } = useAuthStore();

  useEffect(() => {
    const checkGoogleReady = () => {
      setIsReady(!!window.google?.accounts?.id);
    };

    // Check immediately
    checkGoogleReady();

    // Check periodically until ready
    const interval = setInterval(() => {
      if (window.google?.accounts?.id) {
        setIsReady(true);
        clearInterval(interval);
      }
    }, 100);

    // Cleanup
    return () => clearInterval(interval);
  }, []);

  return {
    isReady,
    isAuthenticated,
    isLoading,
  };
};
