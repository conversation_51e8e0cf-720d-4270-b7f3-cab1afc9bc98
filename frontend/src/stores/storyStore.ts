import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  Story, 
  CreateStoryRequest, 
  StoryGenerationProgress, 
  StoryFilters,
  PaginatedData 
} from '@/types';
import { storyService } from '@/services/stories';

interface StoryState {
  // Current stories
  stories: Story[];
  currentStory: Story | null;
  
  // Generation state
  generationProgress: StoryGenerationProgress | null;
  isGenerating: boolean;
  
  // UI state
  isLoading: boolean;
  error: string | null;
  
  // Filters and pagination
  filters: StoryFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  
  // Draft story (for form persistence)
  draftStory: Partial<CreateStoryRequest> | null;

  // Actions
  createStory: (request: CreateStoryRequest) => Promise<Story>;
  getStories: (filters?: StoryFilters, page?: number) => Promise<void>;
  getStoryById: (id: string) => Promise<Story>;
  deleteStory: (id: string) => Promise<void>;
  updateStory: (id: string, updates: Partial<Story>) => Promise<void>;
  
  // Generation actions
  startGeneration: (storyId: string) => void;
  updateGenerationProgress: (progress: StoryGenerationProgress) => void;
  stopGeneration: () => void;
  
  // UI actions
  setCurrentStory: (story: Story | null) => void;
  setFilters: (filters: Partial<StoryFilters>) => void;
  clearFilters: () => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Draft actions
  saveDraft: (draft: Partial<CreateStoryRequest>) => void;
  clearDraft: () => void;
  loadDraft: () => Partial<CreateStoryRequest> | null;
}

const initialFilters: StoryFilters = {
  status: undefined,
  theme: undefined,


};

const initialPagination = {
  page: 1,
  limit: 12,
  total: 0,
  hasNext: false,
  hasPrev: false,
};

export const useStoryStore = create<StoryState>()(
  persist(
    (set, get) => ({
      // Initial state
      stories: [],
      currentStory: null,
      generationProgress: null,
      isGenerating: false,
      isLoading: false,
      error: null,
      filters: initialFilters,
      pagination: initialPagination,
      draftStory: null,

      // Create story action
      createStory: async (request: CreateStoryRequest) => {
        set({ isLoading: true, error: null });
        
        try {
          const story = await storyService.createStory(request);
          
          set((state) => ({
            stories: [story, ...state.stories],
            currentStory: story,
            isLoading: false,
            draftStory: null, // Clear draft after successful creation
          }));
          
          return story;
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to create story',
          });
          throw error;
        }
      },

      // Get stories action
      getStories: async (filters?: StoryFilters, page = 1) => {
        set({ isLoading: true, error: null });
        
        try {
          const { filters: currentFilters, pagination } = get();
          const searchFilters = filters || currentFilters;
          
          const response = await storyService.getStories({
            ...searchFilters,
            page,
            limit: pagination.limit,
          });

          // 处理API响应格式
          const paginatedData = response;

          set({
            stories: paginatedData.items || [],
            pagination: {
              page: paginatedData.page || page,
              limit: paginatedData.limit || pagination.limit,
              total: paginatedData.total || 0,
              hasNext: paginatedData.hasNext || false,
              hasPrev: paginatedData.hasPrev || false,
            },
            filters: searchFilters,
            isLoading: false,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to load stories',
          });
        }
      },

      // Get story by ID action
      getStoryById: async (id: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const story = await storyService.getStoryById(id);
          
          set({
            currentStory: story,
            isLoading: false,
          });
          
          return story;
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to load story',
          });
          throw error;
        }
      },

      // Delete story action
      deleteStory: async (id: string) => {
        set({ isLoading: true, error: null });
        
        try {
          await storyService.deleteStory(id);
          
          set((state) => ({
            stories: state.stories.filter(story => story.id !== id),
            currentStory: state.currentStory?.id === id ? null : state.currentStory,
            isLoading: false,
          }));
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to delete story',
          });
          throw error;
        }
      },

      // Update story action
      updateStory: async (id: string, updates: Partial<Story>) => {
        set({ isLoading: true, error: null });
        
        try {
          const updatedStory = await storyService.updateStory(id, updates);
          
          set((state) => ({
            stories: state.stories.map(story => 
              story.id === id ? updatedStory : story
            ),
            currentStory: state.currentStory?.id === id ? updatedStory : state.currentStory,
            isLoading: false,
          }));
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to update story',
          });
          throw error;
        }
      },

      // Generation actions
      startGeneration: (storyId: string) => {
        set({
          isGenerating: true,
          generationProgress: {
            storyId,
            stage: 'preparing',
            progress: 0,
            currentStep: 'Initializing story generation...',
            stageProgress: {
              preparing: false,
              generating_text: false,
              generating_images: false,
              generating_audio: false,
              composing: false,
              completed: false
            }
          },
        });
      },

      updateGenerationProgress: (progress: StoryGenerationProgress) => {
        set({ generationProgress: progress });
        
        // Update story status if generation is complete
        if (progress.stage === 'completed' || progress.error) {
          set((state) => ({
            isGenerating: false,
            stories: state.stories.map(story =>
              story.id === progress.storyId
                ? { ...story, status: 'completed' }
                : story
            ),
            currentStory: state.currentStory?.id === progress.storyId
              ? { ...state.currentStory, status: 'completed' }
              : state.currentStory,
          }));
        }
      },

      stopGeneration: () => {
        set({
          isGenerating: false,
          generationProgress: null,
        });
      },

      // UI actions
      setCurrentStory: (story: Story | null) => {
        set({ currentStory: story });
      },

      setFilters: (filters: Partial<StoryFilters>) => {
        set((state) => ({
          filters: { ...state.filters, ...filters },
        }));
      },

      clearFilters: () => {
        set({ filters: initialFilters });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      // Draft actions
      saveDraft: (draft: Partial<CreateStoryRequest>) => {
        set({ draftStory: draft });
      },

      clearDraft: () => {
        set({ draftStory: null });
      },

      loadDraft: () => {
        return get().draftStory;
      },

      // Initialize store
      initialize: () => {
        console.log('Story store initialized');
      },
    }),
    {
      name: 'story-storage',
      partialize: (state) => ({
        draftStory: state.draftStory,
        filters: state.filters,
      }),
    }
  )
);

// Selectors
export const useStories = () => {
  const { stories, isLoading, error, pagination } = useStoryStore();
  return { stories, isLoading, error, pagination };
};

export const useCurrentStory = () => {
  const { currentStory, isLoading, error } = useStoryStore();
  return { currentStory, isLoading, error };
};

export const useStoryGeneration = () => {
  const { generationProgress, isGenerating } = useStoryStore();
  return { generationProgress, isGenerating };
};

export const useStoryActions = () => {
  const {
    createStory,
    getStories,
    getStoryById,
    deleteStory,
    updateStory,
    setCurrentStory,
    setFilters,
    clearFilters,
  } = useStoryStore();
  
  return {
    createStory,
    getStories,
    getStoryById,
    deleteStory,
    updateStory,
    setCurrentStory,
    setFilters,
    clearFilters,
  };
};
