import { StoryStatus, STORY_GENERATION_STAGES } from '@/types/story';

/**
 * 获取状态对应的阶段索引
 */
export function getStageIndex(status: StoryStatus): number {
  const stageMap: Record<StoryStatus, number> = {
    'draft': -1,
    'preparing': 0,
    'generating_text': 1,
    'generating_images': 2,
    'generating_audio': 3,
    'composing': 4,
    'completed': 5,
    'failed': -1
  };
  return stageMap[status] ?? -1;
}

/**
 * 根据状态估算进度百分比
 */
export function getProgressPercentage(status: StoryStatus): number {
  const progressMap: Record<StoryStatus, number> = {
    'draft': 0,
    'preparing': 5,
    'generating_text': 25,
    'generating_images': 55,
    'generating_audio': 80,
    'composing': 95,
    'completed': 100,
    'failed': 0
  };
  return progressMap[status] ?? 0;
}

/**
 * 获取状态的显示名称
 */
export function getStatusDisplayName(status: StoryStatus): string {
  const stageIndex = getStageIndex(status);
  if (stageIndex >= 0 && stageIndex < STORY_GENERATION_STAGES.length) {
    return STORY_GENERATION_STAGES[stageIndex].name;
  }
  
  const nameMap: Record<StoryStatus, string> = {
    'draft': '草稿',
    'failed': '生成失败',
    'preparing': '准备生成',
    'generating_text': '创作文本',
    'generating_images': '绘制插图',
    'generating_audio': '合成语音',
    'composing': '最终合成',
    'completed': '生成完毕'
  };
  return nameMap[status] ?? '未知状态';
}

/**
 * 检查状态是否为生成中
 */
export function isGenerating(status: StoryStatus): boolean {
  return ['preparing', 'generating_text', 'generating_images', 'generating_audio', 'composing'].includes(status);
}

/**
 * 检查状态是否已完成
 */
export function isCompleted(status: StoryStatus): boolean {
  return status === 'completed';
}

/**
 * 检查状态是否失败
 */
export function isFailed(status: StoryStatus): boolean {
  return status === 'failed';
}

/**
 * 获取下一个状态
 */
export function getNextStatus(currentStatus: StoryStatus): StoryStatus | null {
  const statusFlow: StoryStatus[] = [
    'preparing',
    'generating_text', 
    'generating_images',
    'generating_audio',
    'composing',
    'completed'
  ];
  
  const currentIndex = statusFlow.indexOf(currentStatus);
  if (currentIndex >= 0 && currentIndex < statusFlow.length - 1) {
    return statusFlow[currentIndex + 1];
  }
  
  return null;
}

/**
 * 计算阶段完成状态
 */
export function getStageProgress(status: StoryStatus) {
  const stageIndex = getStageIndex(status);
  
  return {
    preparing: stageIndex >= 0,
    generating_text: stageIndex >= 1,
    generating_images: stageIndex >= 2,
    generating_audio: stageIndex >= 3,
    composing: stageIndex >= 4,
    completed: stageIndex >= 5
  };
}
