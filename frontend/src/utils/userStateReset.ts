/**
 * 用户状态重置工具
 * 用于解决前后端数据不一致问题
 */

import { useAuthStore } from '../stores/authStore';

/**
 * 强制重置用户状态并清理所有缓存
 * 用于解决数据不一致问题
 */
export const forceResetUserState = (): void => {
  console.warn('🔄 FORCE RESET: 强制重置用户状态');
  
  try {
    // 1. 清除所有本地存储
    console.log('🧹 清除 localStorage...');
    localStorage.removeItem('auth-storage');
    localStorage.removeItem('story-storage');
    localStorage.removeItem('user-preferences');
    
    // 2. 清除会话存储
    console.log('🧹 清除 sessionStorage...');
    sessionStorage.clear();
    
    // 3. 重置 Zustand 认证状态
    console.log('🔄 重置认证状态...');
    const authStore = useAuthStore.getState();
    authStore.logout();
    
    // 4. 清除任何可能的调试用户数据
    if (authStore.resetDebugState) {
      authStore.resetDebugState();
    }
    
    console.log('✅ 用户状态重置完成');

    // 5. 显示用户友好的提示，提供选择
    if (typeof window !== 'undefined') {
      const shouldReset = confirm(
        '检测到认证状态异常，这可能是由于网络问题或缓存过期导致的。\n\n' +
        '点击"确定"清理缓存并重新登录，或点击"取消"刷新页面重试。'
      );

      if (shouldReset) {
        // 用户选择重置，延迟跳转确保清理完成
        setTimeout(() => {
          window.location.href = '/auth?reason=data_reset';
        }, 500);
      } else {
        // 用户选择重试，刷新页面
        window.location.reload();
      }
      return; // 避免继续执行后续代码
    }
    
  } catch (error) {
    console.error('❌ 重置用户状态时出错:', error);
  }
};

/**
 * 检测并修复用户状态不一致问题
 * 改进版本：减少误判，增加容错性
 */
export const detectAndFixUserStateInconsistency = (): boolean => {
  const authStore = useAuthStore.getState();
  const { user, isAuthenticated, tokens } = authStore;

  console.log('🔍 数据一致性检查详情:', {
    isAuthenticated,
    hasUser: !!user,
    hasTokens: !!tokens,
    userId: user?.id,
    userEmail: user?.email,
    userCredits: user?.credits,
    timestamp: new Date().toISOString()
  });

  // 更严格的检查条件，避免误判临时状态
  const hasInconsistency = (
    // 只有在有tokens但没有用户数据时才认为不一致（避免初始化期间的临时状态）
    (isAuthenticated && tokens?.accessToken && !user) ||
    // 检测明显的调试用户特征（而不是仅仅基于积分数量）
    (user && (
      user.id === 'debug-user-001' ||
      user.id === 'test-user' ||
      (user.email === '<EMAIL>') ||
      (user.name === '调试用户') ||
      // 只有当积分异常高且同时具有其他调试特征时才触发
      (user.credits > 10000000 && (
        user.email?.includes('test') ||
        user.email?.includes('debug') ||
        user.name?.includes('test') ||
        user.name?.includes('debug')
      ))
    ))
  );

  if (hasInconsistency) {
    console.error('🚨 检测到用户状态不一致:', {
      isAuthenticated,
      hasTokens: !!tokens,
      userId: user?.id,
      userEmail: user?.email,
      userCredits: user?.credits,
      reason: getInconsistencyReason(user, isAuthenticated, tokens)
    });

    forceResetUserState();
    return true;
  }

  return false;
};

/**
 * 获取数据不一致的具体原因
 */
const getInconsistencyReason = (user: any, isAuthenticated: boolean, tokens: any): string => {
  if (isAuthenticated && tokens?.accessToken && !user) {
    return 'authenticated_without_user_data';
  }
  if (user && user.credits > 1000000) {
    return 'abnormal_high_credits';
  }
  if (user && (user.id === 'debug-user-001' || user.id === 'test-user')) {
    return 'debug_user_detected';
  }
  if (user && user.email === '<EMAIL>') {
    return 'debug_email_detected';
  }
  return 'unknown_inconsistency';
};

/**
 * 验证用户认证状态的有效性
 */
export const validateUserAuthState = async (): Promise<boolean> => {
  const authStore = useAuthStore.getState();
  const { user, tokens } = authStore;
  
  // 检查基本认证状态
  if (!user || !tokens?.accessToken) {
    console.log('ℹ️ 用户未认证或缺少令牌');
    return false;
  }
  
  try {
    // 检查JWT令牌格式
    const tokenParts = tokens.accessToken.split('.');
    if (tokenParts.length !== 3) {
      console.error('❌ JWT令牌格式无效');
      forceResetUserState();
      return false;
    }
    
    // 解析JWT payload
    const payload = JSON.parse(atob(tokenParts[1]));
    console.log('🔍 JWT payload:', {
      userId: payload.userId,
      email: payload.email,
      exp: payload.exp,
      type: payload.type
    });
    
    // 检查令牌是否过期（增加5分钟容差，避免时间同步问题）
    const now = Math.floor(Date.now() / 1000);
    const timeToleranceSeconds = 5 * 60; // 5分钟容差
    if (payload.exp && payload.exp < (now - timeToleranceSeconds)) {
      console.error('❌ JWT令牌已过期', {
        tokenExp: payload.exp,
        currentTime: now,
        timeDiff: now - payload.exp,
        tolerance: timeToleranceSeconds
      });
      forceResetUserState();
      return false;
    }
    
    // 检查用户ID是否匹配
    if (payload.userId !== user.id) {
      console.error('❌ JWT用户ID与本地用户ID不匹配:', {
        jwtUserId: payload.userId,
        localUserId: user.id
      });
      forceResetUserState();
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 验证用户认证状态时出错:', error);
    forceResetUserState();
    return false;
  }
};

/**
 * 强制用户重新登录
 */
export const forceRelogin = (): void => {
  console.log('🔄 强制用户重新登录');
  
  // 清理状态
  forceResetUserState();
  
  // 延迟跳转，确保清理完成
  setTimeout(() => {
    if (typeof window !== 'undefined') {
      window.location.href = '/auth?reason=data_inconsistency';
    }
  }, 1000);
};
