import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, Share2 } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/Layout';
import { ProgressiveStoryViewer } from '@/components/features/ProgressiveStoryViewer';
import { Button } from '@/components/ui/Button';
import { useStoryStore } from '@/stores/storyStore';
import { useNotifications } from '@/stores/uiStore';

const ProgressiveStoryPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotifications();
  const { getStoryById } = useStoryStore();

  // 初始加载故事信息（用于获取基本信息）
  useEffect(() => {
    if (id) {
      getStoryById(id);
    }
  }, [id, getStoryById]);

  const handleComplete = () => {
    showSuccess('故事创作完成！');
    // 跳转到标准的故事详情页面
    navigate(`/stories/${id}`);
  };

  const handleError = (error: string) => {
    showError(error);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: '我的专属故事',
        text: '快来看看我创作的精彩故事！',
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      showSuccess('链接已复制到剪贴板');
    }
  };

  if (!id) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center min-h-screen">
          <p className="text-gray-600">故事ID无效</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50"
      >
        {/* Header */}
        <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={() => navigate('/my-stories')}
                  className="p-2"
                >
                  <ArrowLeft className="w-5 h-5" />
                </Button>
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">
                    故事创作中...
                  </h1>
                  <p className="text-sm text-gray-500">
                    实时查看创作进度
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleShare}
                  className="hidden sm:flex"
                >
                  <Share2 className="w-4 h-4 mr-2" />
                  分享
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Progressive Story Content */}
        <div className="py-8">
          {id && (
            <ProgressiveStoryViewer
              storyId={id}
              onComplete={handleComplete}
              onError={handleError}
              className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"
            />
          )}
        </div>

        {/* Tips */}
        <div className="bg-white border-t border-gray-200">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">💡 温馨提示</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 故事内容会随着AI创作进度实时显示</li>
                <li>• 您可以随时查看已完成的部分内容</li>
                <li>• 请保持页面开启，以获得最佳体验</li>
                <li>• 创作完成后会自动跳转到完整故事页面</li>
              </ul>
            </div>
          </div>
        </div>
      </motion.div>
    </DashboardLayout>
  );
};

export default ProgressiveStoryPage;
