<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
    <!-- AI Presentation Designer: Page 3 - Target Users -->

    <!-- 1. Global Definitions & Styles -->
    <defs>
        <style>
            .system-font { font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; }
            .page-title { font-size: 56px; font-weight: 700; fill: #0F172A; }
            .card-title { font-size: 32px; font-weight: 600; fill: #0F172A; }
            .card-tag-text { font-size: 14px; font-weight: 500; fill: #7C3AED; }
            .card-description { font-size: 18px; font-weight: 400; fill: #64748B; line-height: 1.6; }
            .card-scenario-heading { font-size: 16px; font-weight: 600; fill: #334155; }
            .card-scenario-text { font-size: 16px; font-weight: 400; fill: #64748B; line-height: 1.5; }
        </style>
        <filter id="subtle-shadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="0" dy="5" stdDeviation="10" flood-color="#0F172A" flood-opacity="0.05" />
        </filter>
        <symbol id="icon-creator" viewBox="0 0 24 24">
            <path fill="currentColor" d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM9 4h6v2H9V4zm11 14H4V4h3v3h8V4h3v14z"/>
        </symbol>
        <symbol id="icon-subscriber" viewBox="0 0 24 24">
            <path fill="currentColor" d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm-2 16l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"/>
        </symbol>
        <symbol id="icon-gift" viewBox="0 0 24 24">
            <path fill="currentColor" d="M20 6h-2.18c.11-.31.18-.65.18-1 0-1.66-1.34-3-3-3s-3 1.34-3 3c0 .35.07.69.18 1H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-5-1c0-.55.45-1 1-1s1 .45 1 1-.45 1-1 1-1-.45-1-1zM4 8h16v12H4V8z"/>
        </symbol>
        <symbol id="icon-admin" viewBox="0 0 24 24">
            <path fill="currentColor" d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.3-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1c-.23-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.23.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5z"/>
        </symbol>
    </defs>

    <!-- 2. Page Background -->
    <rect width="1920" height="1080" fill="#F8FAFC"/>

    <!-- 3. Page Header -->
    <g id="page-header" transform="translate(100, 130)">
        <text class="system-font page-title">我们的服务对象：目标用户画像</text>
    </g>

    <!-- 4. Content Grid: 2 rows x 2 columns -->
    <g id="content-grid" transform="translate(100, 210)">
        <!-- Row 1 -->
        <g id="card-1-creator" transform="translate(0, 0)">
            <rect x="0" y="0" width="845" height="400" rx="16" fill="#FFFFFF" filter="url(#subtle-shadow)"/>
            <use href="#icon-creator" x="765" y="30" width="50" height="50" color="#7C3AED" opacity="0.1"/>
            <g transform="translate(40, 40)">
                <text class="system-font card-title" y="5">内容创作者</text>
                <rect x="190" y="-12" width="80" height="30" rx="8" fill="#F3E8FF"/>
                <text class="system-font card-tag-text" x="204" y="8">核心用户</text>
                <text class="system-font card-description" y="70">
                    <tspan x="0" dy="1.6em">希望为孩子创作个性化故事的父母、</tspan>
                    <tspan x="0" dy="1.6em">教师及教育工作者。</tspan>
                </text>
                <text class="system-font card-scenario-heading" y="180">典型场景</text>
                <text class="system-font card-scenario-text" y="200">
                    <tspan x="0" dy="1.6em">父母在睡前为孩子定制一个以孩子自己为</tspan>
                    <tspan x="0" dy="1.6em">主角的冒险故事；教师为课堂创作与教学</tspan>
                    <tspan x="0" dy="1.6em">内容相关的趣味故事，以提高学习兴趣。</tspan>
                </text>
            </g>
        </g>
        <g id="card-2-subscriber" transform="translate(875, 0)">
            <rect x="0" y="0" width="845" height="400" rx="16" fill="#FFFFFF" filter="url(#subtle-shadow)"/>
            <use href="#icon-subscriber" x="765" y="30" width="50" height="50" color="#7C3AED" opacity="0.1"/>
            <g transform="translate(40, 40)">
                <text class="system-font card-title" y="5">付费订阅者</text>
                <rect x="190" y="-12" width="80" height="30" rx="8" fill="#F3E8FF"/>
                <text class="system-font card-tag-text" x="204" y="8">价值用户</text>
                <text class="system-font card-description" y="70">
                    <tspan x="0" dy="1.6em">对故事创作有高频需求，愿意为高级功能</tspan>
                    <tspan x="0" dy="1.6em">（如更丰富的画风）付费的用户。</tspan>
                </text>
                <text class="system-font card-scenario-heading" y="180">典型场景</text>
                <text class="system-font card-scenario-text" y="200">
                    <tspan x="0" dy="1.6em">儿童内容领域的专业创作者，利用平台高效</tspan>
                    <tspan x="0" dy="1.6em">生产多样化的故事内容，并通过平台进行分发</tspan>
                    <tspan x="0" dy="1.6em">和变现。</tspan>
                </text>
            </g>
        </g>
        
        <!-- Row 2 -->
        <g id="card-3-gift-buyer" transform="translate(0, 430)">
            <rect x="0" y="0" width="845" height="400" rx="16" fill="#FFFFFF" filter="url(#subtle-shadow)"/>
            <use href="#icon-gift" x="765" y="30" width="50" height="50" color="#7C3AED" opacity="0.1"/>
            <g transform="translate(40, 40)">
                <text class="system-font card-title" y="5">礼品购买者/收藏者</text>
                <rect x="340" y="-12" width="80" height="30" rx="8" fill="#F3E8FF"/>
                <text class="system-font card-tag-text" x="354" y="8">延伸用户</text>
                <text class="system-font card-description" y="70">
                    <tspan x="0" dy="1.6em">希望将独一无二的数字故事或实体书</tspan>
                    <tspan x="0" dy="1.6em">作为礼物或纪念品的用户。</tspan>
                </text>
                <text class="system-font card-scenario-heading" y="180">典型场景</text>
                <text class="system-font card-scenario-text" y="200">
                    <tspan x="0" dy="1.6em">在孩子生日或家庭纪念日，将共同创作的</tspan>
                    <tspan x="0" dy="1.6em">故事制作成精美的实体书，作为一份充满</tspan>
                    <tspan x="0" dy="1.6em">心意和创意的永久珍藏。</tspan>
                </text>
            </g>
        </g>
        <g id="card-4-admin" transform="translate(875, 430)">
            <rect x="0" y="0" width="845" height="400" rx="16" fill="#FFFFFF" filter="url(#subtle-shadow)"/>
            <use href="#icon-admin" x="765" y="30" width="50" height="50" color="#7C3AED" opacity="0.1"/>
            <g transform="translate(40, 40)">
                <text class="system-font card-title" y="5">平台管理员</text>
                <rect x="190" y="-12" width="80" height="30" rx="8" fill="#F3E8FF"/>
                <text class="system-font card-tag-text" x="204" y="8">内部用户</text>
                 <text class="system-font card-description" y="70">
                    <tspan x="0" dy="1.6em">负责平台运营、数据监控和用户管理的</tspan>
                    <tspan x="0" dy="1.6em">项目团队。</tspan>
                </text>
                <text class="system-font card-scenario-heading" y="180">典型场景</text>
                <text class="system-font card-scenario-text" y="200">
                    <tspan x="0" dy="1.6em">通过管理后台监控用户增长、内容创作趋势</tspan>
                    <tspan x="0" dy="1.6em">和营收状况，为产品迭代和市场策略提供</tspan>
                    <tspan x="0" dy="1.6em">数据支持。</tspan>
                </text>
            </g>
        </g>
    </g>

    <!-- Page Number -->
    <text class="system-font" font-size="16" fill="#94A3B8" x="1820" y="1020" text-anchor="middle">3</text>

</svg>