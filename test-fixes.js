#!/usr/bin/env node

/**
 * StoryWeaver修复验证测试脚本
 * 测试3个关键问题的修复效果
 */

const https = require('https');
const fs = require('fs');

const API_BASE = 'https://storyweaver-api.stawky.workers.dev';
const FRONTEND_BASE = 'https://storyweaver.pages.dev';

// 测试配置
const TEST_CONFIG = {
  timeout: 10000,
  maxRetries: 3,
  pollingInterval: 8000 // 验证新的轮询间隔
};

/**
 * HTTP请求工具函数
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: 'GET',
      timeout: TEST_CONFIG.timeout,
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            headers: res.headers,
            data: res.headers['content-type']?.includes('application/json') 
              ? JSON.parse(data) 
              : data
          };
          resolve(result);
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.end();
  });
}

/**
 * 测试1：验证路由修复
 */
async function testRouteFixing() {
  console.log('\n🔍 测试1：验证路由修复');
  console.log('=' .repeat(50));
  
  try {
    // 测试前端路由是否正确配置
    const frontendResponse = await makeRequest(`${FRONTEND_BASE}/stories/test-id`);
    
    console.log(`✅ 前端路由测试:`);
    console.log(`   状态码: ${frontendResponse.status}`);
    console.log(`   内容类型: ${frontendResponse.headers['content-type']}`);
    
    // 检查是否返回HTML而不是404
    if (frontendResponse.status === 200 && 
        frontendResponse.headers['content-type']?.includes('text/html')) {
      console.log('✅ 路由修复成功：/stories/:id 路由正常工作');
      return true;
    } else {
      console.log('❌ 路由可能仍有问题');
      return false;
    }
    
  } catch (error) {
    console.log(`❌ 路由测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试2：验证状态显示修复
 */
async function testStatusDisplay() {
  console.log('\n🔍 测试2：验证状态显示修复');
  console.log('=' .repeat(50));
  
  try {
    // 测试API状态返回
    const apiResponse = await makeRequest(`${API_BASE}/api/stories`);
    
    console.log(`✅ API状态测试:`);
    console.log(`   状态码: ${apiResponse.status}`);
    
    if (apiResponse.status === 200 && apiResponse.data.success) {
      const stories = apiResponse.data.data.items || [];
      console.log(`   返回故事数量: ${stories.length}`);
      
      // 检查状态值
      const statusValues = stories.map(story => story.status).filter(Boolean);
      const uniqueStatuses = [...new Set(statusValues)];
      
      console.log(`   发现的状态值: ${uniqueStatuses.join(', ')}`);
      
      // 检查是否还有旧的状态值
      const legacyStatuses = uniqueStatuses.filter(status => 
        ['generating', 'text', 'images', 'audio'].includes(status)
      );
      
      if (legacyStatuses.length === 0) {
        console.log('✅ 状态修复成功：没有发现旧的状态值');
        return true;
      } else {
        console.log(`❌ 仍有旧状态值: ${legacyStatuses.join(', ')}`);
        return false;
      }
    } else {
      console.log('❌ API响应异常');
      return false;
    }
    
  } catch (error) {
    console.log(`❌ 状态测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试3：验证智能轮询
 */
async function testSmartPolling() {
  console.log('\n🔍 测试3：验证智能轮询机制');
  console.log('=' .repeat(50));
  
  try {
    const startTime = Date.now();
    let requestCount = 0;
    const intervals = [];
    
    // 模拟轮询行为，测试5次请求
    for (let i = 0; i < 5; i++) {
      const requestStart = Date.now();
      
      try {
        await makeRequest(`${API_BASE}/api/stories`);
        requestCount++;
        
        if (i > 0) {
          const interval = requestStart - intervals[intervals.length - 1];
          console.log(`   请求 ${i + 1}: 间隔 ${interval}ms`);
        }
        
        intervals.push(requestStart);
        
        // 等待8秒（新的基础间隔）
        if (i < 4) {
          await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.pollingInterval));
        }
        
      } catch (error) {
        console.log(`   请求 ${i + 1} 失败: ${error.message}`);
      }
    }
    
    const totalTime = Date.now() - startTime;
    const avgInterval = intervals.length > 1 
      ? (intervals[intervals.length - 1] - intervals[0]) / (intervals.length - 1)
      : 0;
    
    console.log(`✅ 轮询测试结果:`);
    console.log(`   成功请求: ${requestCount}/5`);
    console.log(`   总耗时: ${totalTime}ms`);
    console.log(`   平均间隔: ${Math.round(avgInterval)}ms`);
    console.log(`   目标间隔: ${TEST_CONFIG.pollingInterval}ms`);
    
    // 验证间隔是否合理（允许±1秒误差）
    const intervalDiff = Math.abs(avgInterval - TEST_CONFIG.pollingInterval);
    if (intervalDiff < 1000) {
      console.log('✅ 轮询间隔正常，CDN友好');
      return true;
    } else {
      console.log('⚠️  轮询间隔可能需要调整');
      return false;
    }
    
  } catch (error) {
    console.log(`❌ 轮询测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 StoryWeaver修复验证测试');
  console.log('测试目标：验证3个关键问题的修复效果');
  console.log('时间：' + new Date().toISOString());
  
  const results = {
    route: false,
    status: false,
    polling: false
  };
  
  // 执行测试
  results.route = await testRouteFixing();
  results.status = await testStatusDisplay();
  results.polling = await testSmartPolling();
  
  // 汇总结果
  console.log('\n📊 测试结果汇总');
  console.log('=' .repeat(50));
  console.log(`路由修复: ${results.route ? '✅ 通过' : '❌ 失败'}`);
  console.log(`状态显示: ${results.status ? '✅ 通过' : '❌ 失败'}`);
  console.log(`智能轮询: ${results.polling ? '✅ 通过' : '❌ 失败'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n总体结果: ${passedTests}/${totalTests} 测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有修复验证通过！系统已准备好部署。');
    process.exit(0);
  } else {
    console.log('⚠️  部分测试失败，需要进一步检查。');
    process.exit(1);
  }
}

// 运行测试
runTests().catch(error => {
  console.error('❌ 测试执行失败:', error);
  process.exit(1);
});
