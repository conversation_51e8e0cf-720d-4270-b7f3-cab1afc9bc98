import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, Share2, Download, Heart, MoreHorizontal, RefreshCw } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/Layout';
import { StoryViewer } from '@/components/features/StoryViewer';

import { ProgressiveStoryViewer } from '@/components/features/ProgressiveStoryViewer';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useStoryStore } from '@/stores/storyStore';
import { useNotifications } from '@/stores/uiStore';
import { StoryGenerationClient } from '@/services/durableObjects/storyGenerationClient';
import { StoryGenerationStages } from '@/components/features/StoryGenerationStages';
import { getProgressPercentage, getStageProgress, isGenerating } from '@/utils/storyStatusHelpers';
import { createStoryPoller, SmartPoller } from '@/utils/smartPolling';
import { ContentPreviewModal } from '@/components/features/ContentPreviewModal';
import type { Story, StoryGenerationProgress as StoryGenerationProgressType } from '@/types/story';

const StoryDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [generationProgress, setGenerationProgress] = useState<StoryGenerationProgressType | null>(null);
  const wsClientRef = useRef<StoryGenerationClient | null>(null);
  const smartPollerRef = useRef<SmartPoller | null>(null);

  // 预览模态框状态
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [previewContentType, setPreviewContentType] = useState<'text' | 'image' | 'audio'>('text');
  const [previewTaskId, setPreviewTaskId] = useState<string>('');

  const {
    currentStory,
    isLoading,
    error,
    getStoryById,
    updateStory
  } = useStoryStore();

  const { showSuccess, showError } = useNotifications();

  // 处理内容预览
  const handlePreviewContent = useCallback((type: 'text' | 'image' | 'audio', taskId: string) => {
    console.log('🔍 Opening preview for:', type, taskId);
    setPreviewContentType(type);
    setPreviewTaskId(taskId);
    setPreviewModalOpen(true);
  }, []);

  const handleClosePreview = useCallback(() => {
    setPreviewModalOpen(false);
    setPreviewContentType('text');
    setPreviewTaskId('');
  }, []);

  // 初始加载故事
  useEffect(() => {
    if (id) {
      getStoryById(id);
    }
  }, [id, getStoryById]);

  // 调试：打印故事状态
  useEffect(() => {
    if (currentStory) {
      console.log('🔍 故事状态调试:', {
        id: currentStory.id,
        title: currentStory.title,
        status: currentStory.status,
        pagesLength: currentStory.pages?.length || 0,
        hasPages: !!currentStory.pages,
        firstPageText: currentStory.pages?.[0]?.text || 'no text'
      });
    }
  }, [currentStory]);

  // 设置实时更新机制
  useEffect(() => {
    if (!id || !currentStory) return;

    // 如果故事还在生成中，启动HTTP轮询更新
    if (['preparing', 'generating_text', 'generating_images', 'generating_audio', 'composing'].includes(currentStory.status)) {
      console.log('Story is generating, setting up HTTP polling updates');

      // 禁用WebSocket连接，使用HTTP轮询 - 保留WebSocket代码为将来功能
      // try {
      //   wsClientRef.current = new StoryGenerationClient(id);
      //
      //   // 监听故事完成事件
      //   wsClientRef.current.onStoryCompleted((data) => {
      //     console.log('Story completed via WebSocket:', data);
      //     getStoryById(id); // 重新获取最新数据
      //     showSuccess('故事生成完成', '您的故事已经准备好了！');
      //   });
      //
      //   // 监听进度更新
      //   wsClientRef.current.onTaskUpdate((data) => {
      //     console.log('Task update via WebSocket:', data);
      //     // 更新故事状态
      //     if (data.task) {
      //       const { task } = data;
      //       // 可以在这里更新具体的任务进度
      //       console.log(`Task ${task.type} progress: ${task.progress}%`);
      //     }
      //   });
      //
      //   // 监听任务进度
      //   wsClientRef.current.onTaskProgress((data) => {
      //     console.log('Task progress via WebSocket:', data);
      //     // 更新进度条
      //   });
      //
      //   // 连接WebSocket
      //   wsClientRef.current.connect().catch((error) => {
      //     console.warn('WebSocket connection failed, falling back to polling:', error);
      //     startPolling();
      //   });
      //
      // } catch (error) {
      //   console.warn('Failed to create WebSocket client, using polling:', error);
      //   startPolling();
      // }

      // 使用智能HTTP轮询作为主要方式
      console.log('📊 Starting smart HTTP polling for story progress');
      startSmartPolling();
    }

    return () => {
      // 清理WebSocket连接
      if (wsClientRef.current) {
        wsClientRef.current.disconnect();
        wsClientRef.current = null;
      }

      // 清理智能轮询
      if (smartPollerRef.current) {
        smartPollerRef.current.stop();
        smartPollerRef.current = null;
      }
    };
  }, [id, currentStory?.status]);

  // 智能轮询机制，支持6阶段状态更新和CDN友好策略
  const startSmartPolling = useCallback(() => {
    if (smartPollerRef.current) {
      smartPollerRef.current.stop();
    }

    if (!id) return;

    // 创建智能轮询器
    smartPollerRef.current = createStoryPoller(
      id,
      getStoryById,
      {
        baseInterval: 8000,    // 8秒基础间隔，CDN友好
        maxInterval: 30000,    // 30秒最大间隔
        maxRetries: 5,         // 5次重试
        backoffMultiplier: 1.5, // 1.5倍退避
        jitterRange: 0.1       // 10%抖动
      },
      // 更新回调 - 处理DO状态数据
      (updatedStory) => {
        console.log('📊 Story status updated:', updatedStory.status);
        console.log('📊 DO tasks:', updatedStory.tasks);

        // 如果有DO任务数据，使用详细的任务进度
        if (updatedStory.tasks && Array.isArray(updatedStory.tasks)) {
          const taskProgress = generateTaskProgress(updatedStory.tasks);
          const currentStep = generateCurrentStepMessage(updatedStory.tasks, updatedStory.status);

          setGenerationProgress({
            storyId: updatedStory.id,
            stage: updatedStory.status as any,
            progress: taskProgress.overallProgress,
            currentStep: currentStep,
            stageProgress: taskProgress.stageProgress,
            taskDetails: updatedStory.tasks // 添加任务详情
          });
        } else {
          // 降级到传统进度显示
          setGenerationProgress({
            storyId: updatedStory.id,
            stage: updatedStory.status === 'completed' ? 'completed' :
                   updatedStory.status === 'failed' ? 'completed' :
                   updatedStory.status as any,
            progress: getProgressPercentage(updatedStory.status),
            currentStep: updatedStory.status === 'completed' ? '故事生成完成！' :
                        updatedStory.status === 'failed' ? '生成失败，请重试' :
                        '正在处理中...',
            stageProgress: getStageProgress(updatedStory.status)
          });
        }
      }
    );

    // 开始轮询
    smartPollerRef.current.start(
      // 成功回调
      () => {
        console.log('✅ Story generation completed!');
        showSuccess('故事生成完成', '您的故事已经准备好了！');
      },
      // 错误回调
      (error) => {
        console.error('❌ Smart polling failed:', error);
        showError('连接失败', '无法获取故事状态，请刷新页面重试');
      }
    );

    console.log('🔄 Started smart polling for story:', id);
  }, [id, getStoryById, showSuccess, showError]);

/**
 * 根据DO任务生成详细进度信息
 */
const generateTaskProgress = useCallback((tasks: any[]) => {
  const textTask = tasks.find(t => t.type === 'text');
  const imageTask = tasks.find(t => t.type === 'image');
  const audioTask = tasks.find(t => t.type === 'audio');

  const completedTasks = tasks.filter(t => t.status === 'completed').length;
  const totalTasks = tasks.length;
  const overallProgress = Math.round((completedTasks / totalTasks) * 100);

  return {
    overallProgress,
    stageProgress: {
      preparing: true,
      generating_text: textTask?.status === 'completed',
      generating_images: imageTask?.status === 'completed',
      generating_audio: audioTask?.status === 'completed',
      composing: completedTasks === totalTasks,
      completed: completedTasks === totalTasks
    }
  };
}, []);

/**
 * 根据任务状态生成当前步骤消息
 */
const generateCurrentStepMessage = useCallback((tasks: any[], status: string) => {
  const textTask = tasks.find(t => t.type === 'text');
  const imageTask = tasks.find(t => t.type === 'image');
  const audioTask = tasks.find(t => t.type === 'audio');

  if (status === 'completed') {
    return '🎉 故事生成完成！';
  }

  if (status === 'failed') {
    return '❌ 生成失败，请重试';
  }

  // 根据当前运行的任务显示具体步骤
  if (audioTask?.status === 'running') {
    return '🎵 正在生成语音旁白...';
  } else if (imageTask?.status === 'running') {
    return '🎨 正在绘制故事插图...';
  } else if (textTask?.status === 'running') {
    return '📝 正在创作故事内容...';
  } else if (tasks.every(t => t.status === 'completed')) {
    return '🔄 正在最终合成...';
  } else {
    return '⚙️ 正在准备生成任务...';
  }
}, []);

  // 手动刷新
  const handleRefresh = useCallback(async () => {
    if (!id || isRefreshing) return;

    setIsRefreshing(true);
    try {
      await getStoryById(id);
      showSuccess('刷新成功', '故事内容已更新');
    } catch (error) {
      showError('刷新失败', '请稍后重试');
    } finally {
      setIsRefreshing(false);
    }
  }, [id, isRefreshing, getStoryById, showSuccess, showError]);

  const handlePageChange = useCallback((pageNumber: number) => {
    setCurrentPage(pageNumber);
  }, []);

  const handleComplete = useCallback(() => {
    showSuccess('故事阅读完成', '希望您喜欢这个故事！');
  }, [showSuccess]);

  const handleShare = () => {
    if (navigator.share && currentStory) {
      navigator.share({
        title: currentStory.title,
        text: `来看看这个精彩的故事：${currentStory.title}`,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      showSuccess('链接已复制', '您可以分享给朋友了');
    }
  };

  const handleDownload = () => {
    if (currentStory) {
      // TODO: 实现故事下载功能
      showSuccess('下载开始', '故事正在下载中...');
    }
  };

  const handleFavorite = async () => {
    if (currentStory) {
      try {
        await updateStory(currentStory.id, {
          ...currentStory,
          // TODO: 切换收藏状态
        });
        showSuccess('操作成功', '已更新收藏状态');
      } catch (error) {
        showError('操作失败', '请稍后重试');
      }
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <LoadingSpinner size="lg" label="加载故事中..." />
        </div>
      </DashboardLayout>
    );
  }

  if (error || !currentStory) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              故事加载失败
            </h2>
            <p className="text-gray-600 mb-6">
              {error || '找不到指定的故事'}
            </p>
            <div className="space-x-4">
              <Button onClick={() => navigate('/my-stories')}>
                返回故事列表
              </Button>
              <Button variant="outline" onClick={() => window.location.reload()}>
                重新加载
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // 如果故事正在生成中或内容为空，显示进度页面
  const shouldShowProgress = currentStory && (
    ['preparing', 'generating_text', 'generating_images', 'generating_audio', 'composing'].includes((currentStory as Story).status) ||
    !currentStory.pages ||
    currentStory.pages.length === 0 ||
    !currentStory.pages[0]?.text
  );

  if (shouldShowProgress) {
    return (
      <DashboardLayout>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="min-h-screen"
        >
          {/* Header */}
          <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-between h-16">
                <div className="flex items-center space-x-4">
                  <Button
                    variant="ghost"
                    onClick={() => navigate('/my-stories')}
                    className="p-2"
                  >
                    <ArrowLeft className="w-5 h-5" />
                  </Button>
                  <div>
                    <h1 className="text-lg font-semibold text-gray-900">
                      {currentStory.title}
                    </h1>
                    <p className="text-sm text-gray-500">
                      正在为您创作专属故事...
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Progress Content */}
          <div className="py-8">
            <StoryGenerationStages
              storyStatus={currentStory.status}
              progress={generationProgress}
              className="max-w-4xl mx-auto"
            />
          </div>
        </motion.div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50"
      >
        {/* Header */}
        <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={() => navigate('/my-stories')}
                  className="p-2"
                >
                  <ArrowLeft className="w-5 h-5" />
                </Button>
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">
                    {currentStory?.title || '加载中...'}
                  </h1>
                  <p className="text-sm text-gray-500">
                    {['preparing', 'generating_text', 'generating_images', 'generating_audio', 'composing'].includes(currentStory?.status || '') && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                        生成中...
                      </span>
                    )}
                    {currentStory?.status === 'completed' && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                        已完成
                      </span>
                    )}
                    {currentStory?.pages?.length || 0} 页故事
                    {currentStory?.characterName ? `${currentStory.characterName}的故事` : ''}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {['preparing', 'generating_text', 'generating_images', 'generating_audio', 'composing'].includes(currentStory?.status || '') && (
                  <Button
                    variant="ghost"
                    onClick={handleRefresh}
                    disabled={isRefreshing}
                    className="p-2"
                    title="刷新故事状态"
                  >
                    <RefreshCw className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''}`} />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  onClick={handleFavorite}
                  className="p-2"
                >
                  <Heart className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  onClick={handleShare}
                  className="p-2"
                >
                  <Share2 className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  onClick={handleDownload}
                  className="p-2"
                >
                  <Download className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  className="p-2"
                >
                  <MoreHorizontal className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Story Content */}
        <div className="py-8">
          {currentStory ? (
            // 根据新的6阶段状态系统显示不同内容
            currentStory.status === 'completed' ? (
              // 使用标准查看器显示已完成的故事
              <StoryViewer
                story={currentStory}
                onPageChange={handlePageChange}
                onComplete={handleComplete}
                showControls={false}
                className="max-w-6xl mx-auto"
              />
            ) : currentStory.status === 'failed' ? (
              // 显示失败状态和重试选项
              <div className="max-w-4xl mx-auto">
                <StoryGenerationStages
                  storyStatus={currentStory.status}
                  progress={generationProgress}
                  className="mb-8"
                />
                <div className="text-center">
                  <Button
                    onClick={() => window.location.reload()}
                    className="bg-primary-600 hover:bg-primary-700"
                  >
                    重新生成故事
                  </Button>
                </div>
              </div>
            ) : (
              // 显示生成进度（preparing, generating_text, generating_images, generating_audio, composing）
              <div className="max-w-4xl mx-auto">
                <StoryGenerationStages
                  storyStatus={currentStory.status}
                  progress={generationProgress}
                  className="mb-8"
                />

                {/* 如果有部分内容已生成，可以显示预览 */}
                {currentStory.pages && currentStory.pages.length > 0 && (
                  <div className="mt-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">内容预览</h3>
                    <ProgressiveStoryViewer
                      storyId={currentStory.id}
                      onComplete={() => {
                        getStoryById(currentStory.id);
                        handleComplete();
                      }}
                      onError={(error) => {
                        showError(error);
                      }}
                      className="max-w-6xl mx-auto"
                    />
                  </div>
                )}
              </div>
            )
          ) : (
            <div className="flex justify-center items-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          )}
        </div>

        {/* Story Info */}
        <div className="bg-white border-t border-gray-200">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">故事信息</h3>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>主角：{currentStory?.characterName || '-'}</p>
                  <p>年龄：{currentStory?.characterAge ? `${currentStory.characterAge}岁` : '-'}</p>
                  <p>主题：{currentStory?.theme || '-'}</p>
                  <p>风格：{currentStory?.style || '-'}</p>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">创作时间</h3>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>创建：{currentStory?.createdAt ? new Date(currentStory.createdAt).toLocaleDateString('zh-CN') : '-'}</p>
                  <p>更新：{currentStory?.updatedAt ? new Date(currentStory.updatedAt).toLocaleDateString('zh-CN') : '-'}</p>
                  <p>页数：{currentStory?.pages?.length || 0} 页</p>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">阅读进度</h3>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>当前页：第 {currentPage + 1} 页</p>
                  <p>进度：{currentStory?.pages?.length ? Math.round(((currentPage + 1) / currentStory.pages.length) * 100) : 0}%</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </DashboardLayout>
  );
};

export default StoryDetailPage;
