// 6阶段故事生成状态
export type StoryStatus =
  | 'draft'              // 草稿状态
  | 'preparing'          // 准备生成
  | 'generating_text'    // 生成文本中
  | 'generating_images'  // 生成图片中
  | 'generating_audio'   // 生成语音中
  | 'composing'          // 合成中
  | 'completed'          // 生成完毕
  | 'failed';            // 生成失败

export interface Story {
  id: string;
  title: string;
  characterName: string;
  characterAge: number;
  characterTraits: string[];
  theme: string;
  setting: string;
  style: string;
  voice: string;
  pages: StoryPage[];
  coverImageUrl?: string;
  audioUrl?: string;
  status: StoryStatus;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface StoryPage {
  id: string;
  pageNumber: number;
  text: string;
  imageUrl?: string;
  audioUrl?: string;
  imagePrompt?: string;
  generatedAt?: string;
}

export interface CreateStoryRequest {
  characterName: string;
  characterAge: number;
  characterTraits: string[];
  theme: string;
  setting: string;
  style: string;
  voice: string;
  customPrompt?: string;
  skipAudio?: boolean; // 新增：是否跳过音频生成
}

// 渐进式内容展示相关类型
export interface ProgressivePageState {
  pageIndex: number;
  text: {
    content?: string;
    status: 'pending' | 'generating' | 'completed' | 'error';
    generatedAt?: string;
  };
  image: {
    url?: string;
    prompt?: string;
    status: 'pending' | 'generating' | 'completed' | 'error';
    generatedAt?: string;
  };
  audio: {
    url?: string;
    status: 'pending' | 'generating' | 'completed' | 'error';
    generatedAt?: string;
  };
}

export interface ProgressiveStoryState {
  storyId: string;
  title?: string;
  totalPages?: number;
  pages: ProgressivePageState[];
  overallStatus: 'initializing' | 'generating' | 'completed' | 'error';
  currentStage: 'preparing' | 'generating_text' | 'generating_images' | 'generating_audio' | 'composing' | 'completed';
  progress: {
    text: { completed: number; total: number };
    image: { completed: number; total: number };
    audio: { completed: number; total: number };
  };
}

export interface UpdateStoryRequest {
  title?: string;
  status?: StoryStatus;
}

// 详细的故事生成进度信息
export interface StoryGenerationProgress {
  storyId: string;
  stage: 'preparing' | 'generating_text' | 'generating_images' | 'generating_audio' | 'composing' | 'completed';
  progress: number; // 0-100
  currentStep: string;
  estimatedTimeRemaining?: number;
  error?: string;
  stageProgress: {
    preparing: boolean;
    generating_text: boolean;
    generating_images: boolean;
    generating_audio: boolean;
    composing: boolean;
    completed: boolean;
  };
  taskDetails?: {
    id: string;
    type: 'text' | 'image' | 'audio';
    status: 'pending' | 'running' | 'completed' | 'failed';
    progress: number;
    createdAt: number;
    updatedAt: number;
    error?: string;
  }[];
}

// 6阶段进度状态定义
export interface StoryStageInfo {
  id: keyof StoryGenerationProgress['stageProgress'];
  name: string;
  description: string;
  icon: string;
  estimatedDuration: number; // 预估耗时（秒）
}

// 6阶段配置
export const STORY_GENERATION_STAGES: StoryStageInfo[] = [
  {
    id: 'preparing',
    name: '准备生成',
    description: '正在初始化故事生成环境...',
    icon: 'Settings',
    estimatedDuration: 5
  },
  {
    id: 'generating_text',
    name: '创作文本',
    description: 'AI正在根据您的设定创作精彩的故事内容...',
    icon: 'FileText',
    estimatedDuration: 30
  },
  {
    id: 'generating_images',
    name: '绘制插图',
    description: 'AI正在为故事绘制生动的插图...',
    icon: 'Image',
    estimatedDuration: 45
  },
  {
    id: 'generating_audio',
    name: '合成语音',
    description: 'AI正在为故事配音，生成优美的朗读音频...',
    icon: 'Volume2',
    estimatedDuration: 25
  },
  {
    id: 'composing',
    name: '最终合成',
    description: '正在整合所有内容，完成最终的故事制作...',
    icon: 'Sparkles',
    estimatedDuration: 10
  },
  {
    id: 'completed',
    name: '生成完毕',
    description: '您的专属故事已经准备好了！',
    icon: 'CheckCircle',
    estimatedDuration: 0
  }
];

export interface StoryFilters {
  status?: StoryStatus;
  theme?: string;
  characterAge?: number;
  query?: string;
  sortBy?: 'newest' | 'oldest' | 'title' | 'character';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  dateFrom?: string;
  dateTo?: string;
}

export interface StoryStats {
  total: number;
  completed: number;
  generating: number;
  failed: number;
  byTheme: Record<string, number>;
  byAge: Record<string, number>;
}

// Story themes and settings
export const STORY_THEMES = [
  'adventure',
  'friendship',
  'learning',
  'family',
  'animals',
  'fantasy',
  'science',
  'mystery'
] as const;

export const STORY_SETTINGS = [
  'forest',
  'ocean',
  'city',
  'space',
  'home',
  'school',
  'playground',
  'magical_land'
] as const;

export const STORY_STYLES = [
  'simple',
  'detailed',
  'poetic',
  'humorous',
  'educational',
  'adventurous'
] as const;

export const VOICE_OPTIONS = [
  'gentle_female',
  'warm_male',
  'child_friendly',
  'storyteller',
  'narrator'
] as const;

export type StoryTheme = typeof STORY_THEMES[number];
export type StorySetting = typeof STORY_SETTINGS[number];
export type StoryStyle = typeof STORY_STYLES[number];
export type VoiceOption = typeof VOICE_OPTIONS[number];