import React from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  FileText, 
  Image, 
  Volume2, 
  Sparkles, 
  CheckCircle, 
  Loader2,
  Clock,
  AlertCircle
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { StoryGenerationProgress, STORY_GENERATION_STAGES, StoryStatus } from '@/types/story';
import { cn } from '@/utils/cn';

interface StoryGenerationStagesProps {
  storyStatus: StoryStatus;
  progress?: StoryGenerationProgress;
  className?: string;
}

// 图标映射
const iconMap = {
  Settings,
  FileText,
  Image,
  Volume2,
  Sparkles,
  CheckCircle
};

export const StoryGenerationStages: React.FC<StoryGenerationStagesProps> = ({
  storyStatus,
  progress,
  className
}) => {
  // 根据当前状态计算整体进度
  const calculateOverallProgress = () => {
    if (progress?.progress) {
      return progress.progress;
    }
    
    // 根据状态估算进度
    switch (storyStatus) {
      case 'preparing': return 5;
      case 'generating_text': return 25;
      case 'generating_images': return 55;
      case 'generating_audio': return 80;
      case 'composing': return 95;
      case 'completed': return 100;
      case 'failed': return 0;
      default: return 0;
    }
  };

  // 获取当前阶段索引
  const getCurrentStageIndex = () => {
    const stageMap = {
      'preparing': 0,
      'generating_text': 1,
      'generating_images': 2,
      'generating_audio': 3,
      'composing': 4,
      'completed': 5
    };
    return stageMap[storyStatus as keyof typeof stageMap] ?? 0;
  };

  const overallProgress = calculateOverallProgress();
  const currentStageIndex = getCurrentStageIndex();

  return (
    <div className={cn('w-full', className)}>
      {/* 整体进度条 */}
      <Card className="p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              故事生成进度
            </h3>
            <p className="text-sm text-gray-600">
              {progress?.currentStep || STORY_GENERATION_STAGES[currentStageIndex]?.description || '正在处理中...'}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-primary-600">
              {overallProgress}%
            </div>
            {progress?.estimatedTimeRemaining && progress.estimatedTimeRemaining > 0 && (
              <div className="text-xs text-gray-500 flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                预计剩余 {Math.ceil(progress.estimatedTimeRemaining / 60)} 分钟
              </div>
            )}
          </div>
        </div>
        
        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-3">
          <motion.div
            className="bg-gradient-to-r from-primary-500 to-secondary-500 h-3 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${overallProgress}%` }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          />
        </div>
      </Card>

      {/* 6阶段详细进度 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {STORY_GENERATION_STAGES.map((stage, index) => {
          const IconComponent = iconMap[stage.icon as keyof typeof iconMap];
          const isCompleted = index < currentStageIndex;
          const isCurrent = index === currentStageIndex && storyStatus !== 'completed' && storyStatus !== 'failed';
          const isUpcoming = index > currentStageIndex;
          const isFailed = storyStatus === 'failed' && index === currentStageIndex;

          return (
            <motion.div
              key={stage.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={cn(
                'p-4 transition-all duration-300',
                {
                  'ring-2 ring-primary-500 bg-primary-50': isCurrent,
                  'bg-green-50 border-green-200': isCompleted,
                  'bg-gray-50': isUpcoming,
                  'bg-red-50 border-red-200': isFailed
                }
              )}>
                <div className="flex items-start space-x-3">
                  {/* 图标 */}
                  <div className={cn(
                    'w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0',
                    {
                      'bg-primary-100 text-primary-600': isCurrent,
                      'bg-green-100 text-green-600': isCompleted,
                      'bg-gray-100 text-gray-400': isUpcoming,
                      'bg-red-100 text-red-600': isFailed
                    }
                  )}>
                    {isFailed ? (
                      <AlertCircle className="w-5 h-5" />
                    ) : isCompleted ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : isCurrent ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      <IconComponent className="w-5 h-5" />
                    )}
                  </div>

                  {/* 内容 */}
                  <div className="flex-1 min-w-0">
                    <h4 className={cn(
                      'font-medium text-sm mb-1',
                      {
                        'text-primary-900': isCurrent,
                        'text-green-900': isCompleted,
                        'text-gray-600': isUpcoming,
                        'text-red-900': isFailed
                      }
                    )}>
                      {stage.name}
                    </h4>
                    <p className={cn(
                      'text-xs leading-relaxed',
                      {
                        'text-primary-700': isCurrent,
                        'text-green-700': isCompleted,
                        'text-gray-500': isUpcoming,
                        'text-red-700': isFailed
                      }
                    )}>
                      {isFailed ? '生成失败，请重试' : stage.description}
                    </p>
                    
                    {/* 状态标签 */}
                    <div className="mt-2">
                      <span className={cn(
                        'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                        {
                          'bg-primary-100 text-primary-800': isCurrent,
                          'bg-green-100 text-green-800': isCompleted,
                          'bg-gray-100 text-gray-600': isUpcoming,
                          'bg-red-100 text-red-800': isFailed
                        }
                      )}>
                        {isFailed ? '失败' : isCompleted ? '已完成' : isCurrent ? '进行中' : '等待中'}
                      </span>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* 错误信息显示 */}
      {storyStatus === 'failed' && progress?.error && (
        <Card className="mt-6 p-4 bg-red-50 border-red-200">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-medium text-red-900 mb-1">生成失败</h4>
              <p className="text-sm text-red-700">{progress.error}</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
