import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Clock,
  CheckCircle,
  Loader2,
  Image as ImageIcon,
  Volume2,
  FileText,
  Eye,
  EyeOff
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { StoryGenerationClient, PageContentMessage, GenerationProgressMessage, StoryStructureMessage } from '@/services/durableObjects/storyGenerationClient';
import { ProgressiveStoryState, ProgressivePageState } from '@/types/story';
import { cn } from '@/utils/cn';

interface ProgressiveStoryViewerProps {
  storyId: string;
  onComplete?: () => void;
  onError?: (error: string) => void;
  className?: string;
}

export const ProgressiveStoryViewer: React.FC<ProgressiveStoryViewerProps> = ({
  storyId,
  onComplete,
  onError,
  className
}) => {
  const [storyState, setStoryState] = useState<ProgressiveStoryState>({
    storyId,
    pages: [],
    overallStatus: 'initializing',
    currentStage: 'preparing',
    progress: {
      text: { completed: 0, total: 0 },
      image: { completed: 0, total: 0 },
      audio: { completed: 0, total: 0 }
    }
  });

  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [showCompletedOnly, setShowCompletedOnly] = useState(false);
  const wsClientRef = useRef<StoryGenerationClient | null>(null);

  // WebSocket事件处理器
  const handleWebSocketConnected = useCallback(() => {
    console.log('WebSocket connected for progressive viewer');
  }, []);

  const handleStoryStructure = useCallback((message: StoryStructureMessage) => {
    console.log('Story structure received:', message);
    setStoryState(prev => ({
      ...prev,
      title: message.title,
      totalPages: message.totalPages,
      pages: Array.from({ length: message.totalPages }, (_, index) => ({
        pageIndex: index,
        text: { status: 'pending' },
        image: { status: 'pending' },
        audio: { status: 'pending' }
      })),
      progress: {
        ...prev.progress,
        text: { completed: 0, total: message.totalPages },
        image: { completed: 0, total: message.totalPages },
        audio: { completed: 0, total: 1 } // 通常整个故事只有一个音频文件
      }
    }));
  }, []);

  const handlePageContentReady = useCallback((message: PageContentMessage) => {
    console.log('Page content ready:', message);
    setStoryState(prev => {
      const newPages = [...prev.pages];
      const pageIndex = message.pageIndex;

      if (newPages[pageIndex]) {
        // 更新文本内容
        if (message.content.text) {
          newPages[pageIndex].text = {
            content: message.content.text,
            status: 'completed',
            generatedAt: new Date().toISOString()
          };
        }

        // 更新图片内容
        if (message.content.imageUrl) {
          newPages[pageIndex].image = {
            url: message.content.imageUrl,
            prompt: message.content.imagePrompt,
            status: 'completed',
            generatedAt: new Date().toISOString()
          };
        }

        // 更新音频内容
        if (message.content.audioUrl) {
          newPages[pageIndex].audio = {
            url: message.content.audioUrl,
            status: 'completed',
            generatedAt: new Date().toISOString()
          };
        }
      }

      return {
        ...prev,
        pages: newPages
      };
    });
  }, []);

  const handleGenerationProgress = useCallback((message: GenerationProgressMessage) => {
    console.log('Generation progress:', message);
    setStoryState(prev => ({
      ...prev,
      currentStage: message.stage,
      progress: {
        ...prev.progress,
        [message.stage]: {
          completed: message.progress.completed,
          total: message.progress.total
        }
      }
    }));
  }, []);

  const handleStoryCompleted = useCallback(() => {
    console.log('Story generation completed');
    setStoryState(prev => ({
      ...prev,
      overallStatus: 'completed',
      currentStage: 'completed'
    }));
    onComplete?.();
  }, [onComplete]);

  const handleWebSocketError = useCallback((error: unknown) => {
    console.error('WebSocket error:', error);
    onError?.('连接出现问题，正在尝试重新连接...');
  }, [onError]);

  // 初始化WebSocket连接
  useEffect(() => {
    const initWebSocket = async () => {
      try {
        wsClientRef.current = new StoryGenerationClient(storyId);

        // 监听各种WebSocket事件
        wsClientRef.current.onConnected(handleWebSocketConnected);
        wsClientRef.current.onStoryStructure(handleStoryStructure);
        wsClientRef.current.onPageContentReady(handlePageContentReady);
        wsClientRef.current.onGenerationProgress(handleGenerationProgress);
        wsClientRef.current.onStoryCompleted(handleStoryCompleted);
        wsClientRef.current.onError(handleWebSocketError);

        await wsClientRef.current.connect();
        console.log('✅ ProgressiveStoryViewer WebSocket connected');
      } catch (error) {
        console.error('❌ Failed to connect WebSocket:', error);
        onError?.('无法连接到服务器，请刷新页面重试');
      }
    };

    initWebSocket();

    return () => {
      wsClientRef.current?.disconnect();
    };
  }, [storyId, handleWebSocketConnected, handleStoryStructure, handlePageContentReady, handleGenerationProgress, handleStoryCompleted, handleWebSocketError, onError]);

  // 计算整体进度
  const calculateOverallProgress = () => {
    const { text, image, audio } = storyState.progress;
    const totalTasks = text.total + image.total + audio.total;
    const completedTasks = text.completed + image.completed + audio.completed;
    return totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
  };

  // 获取可查看的页面（已有内容的页面）
  const getViewablePages = () => {
    return storyState.pages.filter(page => 
      page.text.status === 'completed' || 
      page.image.status === 'completed' || 
      page.audio.status === 'completed'
    );
  };

  // 获取当前显示的页面
  const getCurrentPage = () => {
    const viewablePages = showCompletedOnly ? 
      storyState.pages.filter(page => 
        page.text.status === 'completed' && 
        page.image.status === 'completed'
      ) : 
      getViewablePages();
    
    return viewablePages[currentPageIndex] || null;
  };

  const overallProgress = calculateOverallProgress();
  const viewablePages = getViewablePages();
  const currentPage = getCurrentPage();

  return (
    <div className={cn('w-full max-w-6xl mx-auto', className)}>
      {/* 整体进度指示器 */}
      <Card className="mb-6 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {storyState.title || '正在创作您的专属故事...'}
            </h2>
            <p className="text-sm text-gray-500">
              总体进度 {overallProgress}% • 当前阶段: {
                storyState.currentStage === 'preparing' ? '准备生成' :
                storyState.currentStage === 'generating_text' ? '创作文本' :
                storyState.currentStage === 'generating_images' ? '绘制插图' :
                storyState.currentStage === 'generating_audio' ? '合成语音' :
                storyState.currentStage === 'composing' ? '最终合成' : '已完成'
              }
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowCompletedOnly(!showCompletedOnly)}
              className="text-xs"
            >
              {showCompletedOnly ? <Eye className="w-4 h-4 mr-1" /> : <EyeOff className="w-4 h-4 mr-1" />}
              {showCompletedOnly ? '显示全部' : '仅完成'}
            </Button>
          </div>
        </div>
        
        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <motion.div
            className="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${overallProgress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>

        {/* 阶段指示器 */}
        <div className="grid grid-cols-3 gap-4">
          {[
            { stage: 'generating_text', name: '故事文本', icon: FileText, progress: storyState.progress.text },
            { stage: 'generating_images', name: '精美插图', icon: ImageIcon, progress: storyState.progress.image },
            { stage: 'generating_audio', name: '语音朗读', icon: Volume2, progress: storyState.progress.audio }
          ].map(({ stage, name, icon: Icon, progress }) => (
            <div key={stage} className="flex items-center space-x-2">
              <div className={cn(
                'w-8 h-8 rounded-full flex items-center justify-center',
                storyState.currentStage === stage ? 'bg-primary-100 text-primary-600' :
                progress.completed === progress.total && progress.total > 0 ? 'bg-green-100 text-green-600' :
                'bg-gray-100 text-gray-400'
              )}>
                {progress.completed === progress.total && progress.total > 0 ? (
                  <CheckCircle className="w-4 h-4" />
                ) : storyState.currentStage === stage ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Icon className="w-4 h-4" />
                )}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">{name}</p>
                <p className="text-xs text-gray-500">
                  {progress.completed}/{progress.total}
                </p>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* 故事内容区域 */}
      {storyState.totalPages === undefined ? (
        <Card className="p-8 text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">正在初始化故事创作...</p>
        </Card>
      ) : viewablePages.length === 0 ? (
        <Card className="p-8 text-center">
          <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">故事正在创作中，请稍候...</p>
        </Card>
      ) : (
        <>
          {/* 页面导航 */}
          {viewablePages.length > 1 && (
            <div className="flex justify-center mb-6">
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentPageIndex(Math.max(0, currentPageIndex - 1))}
                  disabled={currentPageIndex === 0}
                >
                  上一页
                </Button>
                <span className="text-sm text-gray-600">
                  {currentPageIndex + 1} / {viewablePages.length}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentPageIndex(Math.min(viewablePages.length - 1, currentPageIndex + 1))}
                  disabled={currentPageIndex === viewablePages.length - 1}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}

          {/* 当前页面内容 */}
          {currentPage && (
            <ProgressiveStoryPage
              page={currentPage}
              pageNumber={currentPage.pageIndex + 1}
            />
          )}
        </>
      )}
    </div>
  );
};

// 单个页面组件
interface ProgressiveStoryPageProps {
  page: ProgressivePageState;
  pageNumber: number;
}

const ProgressiveStoryPage: React.FC<ProgressiveStoryPageProps> = ({ page, pageNumber }) => {
  return (
    <Card className="p-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 图片区域 */}
        <div className="order-2 lg:order-1">
          <div className="aspect-square rounded-xl overflow-hidden bg-gray-100">
            {page.image.status === 'completed' && page.image.url ? (
              <motion.img
                src={page.image.url}
                alt={`故事插图 - 第${pageNumber}页`}
                className="w-full h-full object-cover"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              />
            ) : page.image.status === 'generating' ? (
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center">
                  <Loader2 className="w-8 h-8 text-primary-500 animate-spin mx-auto mb-2" />
                  <p className="text-sm text-gray-600">正在绘制插图...</p>
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center">
                  <ImageIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">等待插图生成</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 文本区域 */}
        <div className="order-1 lg:order-2">
          <div className="h-full flex flex-col">
            <div className="flex-1">
              {page.text.status === 'completed' && page.text.content ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  className="prose prose-lg text-gray-800 leading-relaxed"
                >
                  {page.text.content}
                </motion.div>
              ) : page.text.status === 'generating' ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-center">
                    <Loader2 className="w-6 h-6 text-primary-500 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-gray-600">正在创作文本...</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <div className="text-center">
                    <FileText className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">等待文本生成</p>
                  </div>
                </div>
              )}
            </div>

            {/* 音频控件 */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              {page.audio.status === 'completed' && page.audio.url ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <audio controls className="w-full">
                    <source src={page.audio.url} type="audio/mpeg" />
                    您的浏览器不支持音频播放。
                  </audio>
                </motion.div>
              ) : page.audio.status === 'generating' ? (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>正在合成语音...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Volume2 className="w-4 h-4" />
                  <span>等待语音合成</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};
