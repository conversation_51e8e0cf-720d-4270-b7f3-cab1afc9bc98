import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Sparkles } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useStoryStore } from '@/stores/storyStore';
import { useNotifications } from '@/stores/uiStore';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { ProgressBar } from '@/components/ui/LoadingSpinner';
import { CharacterSetup } from './story-creator/CharacterSetup';
import { ThemeSelection } from './story-creator/ThemeSelection';
import { StyleConfiguration } from './story-creator/StyleConfiguration';
import { StoryPreview } from './story-creator/StoryPreview';
import { GenerationProgress } from './story-creator/GenerationProgress';
import { CreateStoryRequest } from '@/types';

interface StoryCreatorProps {
  className?: string;
}

type Step = 'character' | 'theme' | 'style' | 'preview' | 'generating';

const stepIds = ['character', 'theme', 'style', 'preview'] as const;

export const StoryCreator: React.FC<StoryCreatorProps> = ({ className }) => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState<Step>('character');
  const [storyData, setStoryData] = useState<Partial<CreateStoryRequest>>({});
  const [isValid, setIsValid] = useState(false);

  const navigate = useNavigate();
  const { createStory, isGenerating, generationProgress } = useStoryStore();
  const { showSuccess, showError } = useNotifications();

  const getSteps = () => [
    { id: 'character', title: t('create.steps.character'), description: t('create.character.subtitle') },
    { id: 'theme', title: t('create.steps.theme'), description: t('create.theme.subtitle') },
    { id: 'style', title: t('create.steps.style'), description: t('create.style.subtitle') },
    { id: 'preview', title: t('create.steps.preview'), description: t('create.preview.subtitle') },
  ] as const;

  // Load draft from store on mount
  useEffect(() => {
    const draft = useStoryStore.getState().loadDraft();
    if (draft) {
      setStoryData(draft);
    }
  }, []);

  // Save draft whenever story data changes
  useEffect(() => {
    if (Object.keys(storyData).length > 0) {
      useStoryStore.getState().saveDraft(storyData);
    }
  }, [storyData]);

  const updateStoryData = useCallback((updates: Partial<CreateStoryRequest>) => {
    setStoryData(prev => ({ ...prev, ...updates }));
  }, []);

  const getCurrentStepIndex = () => {
    return stepIds.findIndex(stepId => stepId === currentStep);
  };

  const canGoNext = () => {
    switch (currentStep) {
      case 'character':
        return !!(storyData.characterName && storyData.characterAge && storyData.characterTraits?.length);
      case 'theme':
        return !!(storyData.theme && storyData.setting);
      case 'style':
        return !!(storyData.style && storyData.voice);
      case 'preview':
        return isValid;
      default:
        return false;
    }
  };

  const canGoPrevious = () => {
    return getCurrentStepIndex() > 0 && !isGenerating;
  };

  const handleNext = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex < stepIds.length - 1) {
      setCurrentStep(stepIds[currentIndex + 1] as Step);
    } else if (currentStep === 'preview') {
      handleCreateStory();
    }
  };

  const handlePrevious = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex > 0) {
      setCurrentStep(stepIds[currentIndex - 1] as Step);
    }
  };

  const handleCreateStory = async () => {
    if (!isValid || !storyData.characterName) return;

    try {
      const story = await createStory(storyData as CreateStoryRequest);

      showSuccess(t('create.success.title', '故事创建成功'), t('create.success.message', '正在为您生成精彩的故事内容...'));

      // Clear draft after successful creation
      useStoryStore.getState().clearDraft();

      // 立即跳转到故事详情页面，不再停留在生成进度页面
      navigate(`/story/${story.id}`);
    } catch (error) {
      showError(t('create.error.title', '创建失败'), error instanceof Error ? error.message : t('create.error.message', '请稍后重试'));
      setCurrentStep('preview');
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'character':
        return (
          <CharacterSetup
            data={storyData}
            onChange={updateStoryData}
            onValidationChange={setIsValid}
          />
        );
      case 'theme':
        return (
          <ThemeSelection
            data={storyData}
            onChange={updateStoryData}
            onValidationChange={setIsValid}
          />
        );
      case 'style':
        return (
          <StyleConfiguration
            data={storyData}
            onChange={updateStoryData}
            onValidationChange={setIsValid}
          />
        );
      case 'preview':
        return (
          <StoryPreview
            data={storyData as CreateStoryRequest}
            onValidationChange={setIsValid}
          />
        );
      // 移除generating步骤，因为现在直接跳转到故事详情页面
      // case 'generating':
      //   return (
      //     <GenerationProgress
      //       progress={generationProgress}
      //       storyData={storyData as CreateStoryRequest}
      //     />
      //   );
      default:
        return null;
    }
  };

  if (currentStep === 'generating') {
    return (
      <div className={className}>
        {renderStepContent()}
      </div>
    );
  }

  return (
    <div className={className}>
      <Card className="max-w-4xl mx-auto">
        {/* Progress indicator */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Sparkles className="w-6 h-6 text-primary-500 mr-2" />
              {t('create.title')}
            </h1>
            <div className="text-sm text-gray-500">
              {t('create.stepProgress', { current: getCurrentStepIndex() + 1, total: stepIds.length })}
            </div>
          </div>
          
          <ProgressBar
            value={getCurrentStepIndex() + 1}
            max={stepIds.length}
            className="mb-4"
          />
          
          <div className="flex justify-between text-sm">
            {getSteps().map((step, index) => (
              <div
                key={step.id}
                className={`flex flex-col items-center ${
                  index <= getCurrentStepIndex() ? 'text-primary-600' : 'text-gray-400'
                }`}
              >
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium mb-1 ${
                    index < getCurrentStepIndex()
                      ? 'bg-primary-500 text-white'
                      : index === getCurrentStepIndex()
                      ? 'bg-primary-100 text-primary-600 border-2 border-primary-500'
                      : 'bg-gray-200 text-gray-400'
                  }`}
                >
                  {index < getCurrentStepIndex() ? '✓' : index + 1}
                </div>
                <span className="font-medium">{step.title}</span>
                <span className="text-xs text-gray-500 text-center">{step.description}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Step content */}
        <div className="p-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStepContent()}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Navigation */}
        <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={!canGoPrevious()}
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            {t('common.previous')}
          </Button>
          
          <Button
            onClick={handleNext}
            disabled={!canGoNext()}
          >
            {currentStep === 'preview' ? (
              <>
                <Sparkles className="w-4 h-4 mr-1" />
                {t('create.preview.startCreating')}
              </>
            ) : (
              <>
                {t('common.next')}
                <ChevronRight className="w-4 h-4 ml-1" />
              </>
            )}
          </Button>
        </div>
      </Card>
    </div>
  );
};
