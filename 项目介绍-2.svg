<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
    <!-- AI Presentation Designer: Page 2 - Overview & Value Proposition -->

    <!-- 1. Global Definitions & Styles -->
    <defs>
        <style>
            .system-font { font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; }
            .page-title { font-size: 56px; font-weight: 700; fill: #0F172A; }
            .section-heading { font-size: 28px; font-weight: 600; fill: #0F172A; }
            .value-prop-text { font-size: 32px; font-weight: 500; fill: #0F172A; line-height: 1.5; }
            .body-text { font-size: 20px; font-weight: 400; fill: #64748B; line-height: 1.6; }
        </style>
        <filter id="subtle-shadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="0" dy="5" stdDeviation="10" flood-color="#0F172A" flood-opacity="0.05" />
        </filter>
    </defs>

    <!-- 2. Page Background -->
    <rect width="1920" height="1080" fill="#F8FAFC" />

    <!-- 3. Page Header -->
    <g id="page-header" transform="translate(100, 140)">
        <text class="system-font page-title">项目概览与价值主张</text>
    </g>

    <!-- 4. Main Content -->
    <g id="main-content" transform="translate(100, 240)">

        <!-- 4.1. Value Proposition Card -->
        <g id="value-prop-card">
            <rect x="0" y="0" width="1720" height="360" rx="16" fill="#FFFFFF" filter="url(#subtle-shadow)" />
            <text class="system-font" font-size="150" font-weight="bold" fill="#7C3AED" opacity="0.3" x="40" y="140">“</text>
            <text class="system-font value-prop-text" x="70" y="80">
                <tspan x="70" dy="0">一个为家庭和创作者打造的 AI 驱动的一站式故事创作与变现平台，</tspan>
                <tspan x="70" dy="1.5em">让用户能够轻松地将创意转化为包含精美插图和动人音频的多媒体故事，</tspan>
                <tspan x="70" dy="1.5em">并可进一步定制为可永久珍藏的实体书籍。</tspan>
            </text>
        </g>

        <!-- 4.2. Background & Goal Section -->
        <g id="background-goal-section" transform="translate(0, 420)">
            <text class="system-font section-heading" y="0">背景与目标：我们解决的问题</text>
            <g class="system-font body-text" transform="translate(0, 60)">
                <!-- Point 1 -->
                <circle cx="10" cy="10" r="5" fill="#7C3AED" />
                <text x="30" y="15">解决个性化、高质量儿童内容稀缺的痛点。</text>
                <!-- Point 2 -->
                <circle cx="10" cy="50" r="5" fill="#7C3AED" />
                <text x="30" y="55">为时间有限、缺乏专业工具的家长和教育工作者赋能。</text>
                <!-- Point 3 -->
                <circle cx="10" cy="90" r="5" fill="#7C3AED" />
                <text x="30" y="95">让故事创作不再是少数人的专利，而是每个家庭都能参与的亲子活动。</text>
            </g>
        </g>
    </g>
    
    <!-- Page Number -->
    <text class="system-font" font-size="16" fill="#94A3B8" x="1820" y="1020" text-anchor="middle">2</text>

</svg>